<template>
  <div v-if="bannerList.length > 0">
    <div id="redPacket" v-if="$store.state.appInfo.redpacket_switch == 1 && hongbashow">
      <i @click="$parent.goNav('/hongbao')" class="grab"></i>
      <img @click="changhongbashow" src="/static/image/hongbaocolse.png" />
    </div>
    <!-- 新的头部导航 -->
    <div class="header-nav">
      <div class="header-logo">      
        <img src="/static/style/ng_top.png" alt="logo" style="width: 35px;"/>
      </div>
      <div class="header-title">大厅</div>
      <div class="header-actions">
        <div class="download-btn" @click="$parent.goNav('/app')">
          <img src="/static/style/down.png" alt="下载" />
        </div>
         <img @click="$parent.goNav('/message')" style="width: 26px" src="/static/style/right_notice.png" alt="更多公告" />
      </div>
    </div>

    <div style="position: relative;">
      <van-swipe @change="onChange" >
        <van-swipe-item v-for="(item, index) in bannerList" :key="index" style="height: 200px;">
          <img :src="item.src" style="width: 100%" alt="" />
        </van-swipe-item>
        <template #indicator>
          <div class="swiper-dots">
            <div class="num">{{ current + 1 }}</div>
            <div class="sign">/</div>
            <div class="num">4</div>
          </div>
        </template>
      </van-swipe>
    </div>

    <!-- 公告 -->
    <div style="background-color: white; padding: 6px 0 0" v-if="homenoticelis.length > 0">
      <div class="gonggao">
        <img style="width: 20px" src="/static/image/home_notice.66939586521463adbe87e2a72aa8ecad.png" alt="" />
        <div style="flex: 1">
          <van-notice-bar color="#666" background="white" scrollable
            ><span v-for="(item, index) in homenoticelis" @click="openGogao(item)" :key="index">{{ item }}</span></van-notice-bar
          >
        </div>
      </div>
    </div>
    <!-- 公告 -->
    <div class="domainModal_domainView__FWCzg" v-if="goInfo">
      <div class="domainModal_mask__24Y2m domainModal_fadeIn__1I3AS false" @click="goInfo = null"></div>
      <div class="domainModal_content__1nBgc" style="width: 80%">
        <img src="/static/image/hongbaocolse.png" @click="goInfo = null" style="position: absolute; top: 5px; right: 13px; width: 0.7rem" alt="" />

        <div class="domainModal_middle__3gQPm" style="padding: 35px 10px 15px">
          {{ goInfo }}

          <van-button type="info" style="margin: 0 auto; margin-top: 20px; width: 120px; border-radius: 10px; height: 35px" @click="$parent.goNav('/message')">更多公告</van-button>
        </div>
      </div>
    </div>
    <!-- 用户信息栏 -->
    <div class="xiding">
      <div class="caosuo">
        <!-- 左侧登录注册区域 -->
        <div class="lefsg" v-if="!$store.state.token">
          <div class="login-register-area">
            <div class="auth-buttons">
              <img src="/static/style/denglu.2f9b07eb.png" alt="登录" class="login-btn" @click="$parent.goNav('/login')" />
              <img src="/static/style/zhuce.5eb10ef3.png" alt="注册" class="register-btn" @click="$parent.goNav('/login?type=1')" />
            </div>
            <div class="auth-text">请先登录或注册</div>
          </div>
        </div>
        <!-- 已登录用户信息 -->
        <div v-else class="lefsg">
          <div class="name">{{ $store.state.userInfo.username }}</div>
          <div class="mey"><span>￥</span>{{ $store.state.userInfo.balance }}</div>
        </div>
        <!-- 右侧功能按钮 -->
        <div class="riggs">
          <div class="lisImg" @click="$parent.goNav('/recharge')">
            <img src="/static/style/feature_moneydraw.cbeb513bc9fd00ecc5372fc0e54a84c0.png" alt="" />
            充值
          </div>
          <div class="lisImg" @click="$parent.goNav('/withdrawal')">
            <img src="/static/style/feature_moneysave.4b60fbaf4ee368d1e7060133f62690c9.png" alt="" />
            提现
          </div>
          <div class="lisImg" @click="$parent.goNav('/transfer')">
            <img src="/static/style/feature_moneytransfer.bbfbe4e344c0e5f91eabadd9e20b1613.png" alt="" />
            转账
          </div>
          <div class="lisImg" @click="$parent.goNav('/vip')">
            <img src="/static/style/feature_vip.16d90880db727a342209f4439a9b3a12.png" alt="" />
            VIP
          </div>
        </div>
      </div>
    </div>
    <!-- 游戏栏 -->
    <div class="gameBoxs">
      <div class="lfst">
        <div :class="gameType == 0 ? 'ls active' : 'ls'" @click="changGameType(0)">
          <img :src="`/static/style/${gameType == 0 ? 'sidebar_slot_icon_select' : 'sidebar_slot_icon_nor'}.png`" alt="" />
        </div>
        <div :class="gameType == 1 ? 'ls active' : 'ls'" @click="changGameType(1)">
          <img :src="`/static/style/${gameType == 1 ? 'sidebar_sports_icon_select' : 'sidebar_sports_icon_nor'}.png`" alt="" />
        </div>
        <div :class="gameType == 2 ? 'ls active' : 'ls'" @click="changGameType(2)">
          <img :src="`/static/style/${gameType == 2 ? 'sidebar_lottery_icon_select' : 'sidebar_lottery_icon_nor'}.png`" alt="" />
        </div>
        <div :class="gameType == 3 ? 'ls active' : 'ls'" @click="changGameType(3)">
          <img :src="`/static/style/${gameType == 3 ? 'sidebar_esports_icon_select' : 'sidebar_esports_icon_nor'}.png`" alt="" />
        </div>
        <div :class="gameType == 4 ? 'ls active' : 'ls'" @click="changGameType(4)">
          <img :src="`/static/style/${gameType == 4 ? 'sidebar_casino_icon_select' : 'sidebar_casino_icon_nor'}.png`" alt="" />
        </div>
        <div :class="gameType == 5 ? 'ls active' : 'ls'" @click="changGameType(5)">
          <img :src="`/static/style/${gameType == 5 ? 'sidebar_board_icon_select' : 'sidebar_board_icon_nor'}.png`" alt="" />
        </div>
        <div :class="gameType == 6 ? 'ls active' : 'ls'" @click="changGameType(6)">
          <img :src="`/static/style/${gameType == 6 ? 'sidebar_fishing_icon_select' : 'sidebar_fishing_icon_nor'}.png`" alt="" />
        </div>
      </div>
      <!-- 天美社区源码网 timibbs.net timibbs.com timibbs.vip -->
      <!-- 电子游戏 -->
      <div class="rigts" v-if="gameType == 0">
        <!-- 动态渲染的游戏列表 -->
        <div class="game-card" v-for="item in $store.state.conciseList" :key="item.platform_name + '_' + item.game_code" @click="$parent.openGamePage(item.platform_name, item.game_code, '')">
          <img :src="getGameImageSrc('concise', item.platform_name)" alt="" />
        </div>
      </div>
      <!-- 体育游戏 -->
      <div class="rigts" v-if="gameType == 1">
        <div class="game-card" v-for="item in $store.state.sportList" :key="item.platform_name + '_' + item.game_code" @click="$parent.openGamePage(item.platform_name, item.game_code, '')">
          <img :src="getGameImageSrc('sport', item.platform_name)" alt="" />
        </div>
      </div>
      <!-- 真人游戏 -->
      <div class="rigts" v-if="gameType == 4">
        <div class="game-card" v-for="item in $store.state.realbetList" :key="item.platform_name + '_' + item.game_code" @click="$parent.openGamePage(item.platform_name, item.game_code, '')">
          <img :src="getGameImageSrc('realbet', item.platform_name)" alt="" />
        </div>
      </div>
      <!-- 电竞游戏 -->
      <div class="rigts" v-if="gameType == 3">
        <div class="game-card" v-for="item in $store.state.gamingList" :key="item.platform_name + '_' + item.game_code" @click="$parent.openGamePage(item.platform_name, item.game_code, '')">
          <img :src="getGameImageSrc('gaming', item.platform_name)" alt="" />
        </div>
      </div>
      <!-- 彩票游戏 -->
      <div class="rigts" v-if="gameType == 2">
        <div class="game-card" v-for="(item, index) in $store.state.lotteryList" :key="item.platform_name + '_' + item.game_code" @click="$parent.openGamePage(item.platform_name, item.game_code, '')">
          <img :src="getGameImageSrc('lottery', getLotteryImageCode(item, index))" alt="" />
        </div>
      </div>
      <!-- 棋牌游戏 -->
      <div class="rigts" v-if="gameType == 5">
        <div class="game-card" v-for="item in $store.state.jokerList" :key="item.platform_name + '_' + item.game_code" @click="$parent.openGamePage(item.platform_name, item.game_code, '')">
          <img :src="getGameImageSrc('joker', item.platform_name)" alt="" />
        </div>
      </div>
      <!-- 捕鱼游戏 -->
      <div class="rigts" v-if="gameType == 6">
        <div class="game-card" v-for="item in $store.state.fishingList" :key="item.platform_name + '_' + item.game_code" @click="$parent.openGamePage(item.platform_name, item.game_code, '')">
          <img :src="getGameImageSrc('fishing', item.platform_name)" alt="" />
        </div>
      </div>
    </div>

    <!-- 弹出层 -->
    <van-popup v-model="leftshow" position="left" :style="{ height: '100%' }">
      <div class="leftbox">
        <div class="side__main__1NhyG">
          <h3>Hi，欢迎进入{{ $store.state.appInfo.title }}</h3>
          <dl class="side__vip__1dW8w">
            <div class="topxs">专属VIP体验</div>
            <p>立享会员特权</p>
            <p>享受只属于你的与众不同</p>
            <dd @click="$parent.goNav('/vip')">会员中心</dd>
          </dl>
          <ul>
            <li v-if="$store.state.token" @click="$parent.goNav('/message')"><img src="/static/image/meunIcon.39f38dc98ad956615952d485d0e6af04.svg" />消息中心<span class="side__subtitle__3QtYC"></span></li>
            <li @click="$parent.openKefu"><img src="/static/image/meunIcon2.5d0d78496889fb8b027f603254286fdf.svg" />意见反馈<span class="side__subtitle__3QtYC"></span></li>
            <li @click="doCopy($store.state.appInfo.h5_url)">
              <img src="/static/image/menuIcon5.5687ef4d1512d53aa3535e3b1088fe70.png" />永久域名<span class="side__subtitle__3QtYC">{{ $store.state.appInfo.h5_url }}</span>
            </li>
            <li @click="$parent.goNav('/abouts')"><img src="/static/image/meunIcon3.c51bbb9ebece978f1976397ab12acba7.svg" />关于我们<span class="side__subtitle__3QtYC"></span></li>
          </ul>
          <div class="nisd" v-if="!$store.state.token" @click="$parent.goNav('/login')">立即登录</div>
          <div class="nisd" v-else @click="$parent.outLogin"><img src="/static/image/tuichu.93c1b9e3d4b4a7772481916ca32c074f.svg" />安全退出</div>
        </div>
      </div>
    </van-popup>

    <!-- 官网弹窗 -->
    <div class="domainModal_domainView__FWCzg" v-if="$store.state.appInfo.index_modal == 1 && tanshow">
      <div class="domainModal_mask__24Y2m domainModal_fadeIn__1I3AS false" @click="changtanshow"></div>
      <div class="domainModal_content__1nBgc" style="width: 80%">
        <div id="domain" class="domainModal_contentTop__2C4jc">
          <img src="/static/image/hongbaocolse.png" @click="changtanshow" style="position: absolute; top: 5px; right: 13px; width: 0.6rem" alt="" />

          <div class="domainModal_top__1omYS">欢迎来到{{ $store.state.appInfo.title }}</div>
          <div class="domainModal_middle__3gQPm" v-html="$store.state.appInfo.webcontent"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'index',
  data() {
    return {
      hongbashow: true,
      appShow: true,
      current: 0,
      bannerList: [],
      homenoticelis: [],
      // 弹出层
      leftshow: false,
      activeKey: 0,
      gameType: 0,
      tanshow: true,
      appshow: true,
      goInfo: null,
    };
  },
  created() {
    let that = this;
    that.getBanList();
    that.homenotice(); //获取公告
  },
  methods: {
    // 检查游戏是否已存在于动态列表中，避免重复显示
    hasGameInList(gameCode) {
      return this.$store.state.conciseList.some(item =>
        item.platform_name.toLowerCase() === gameCode.toLowerCase() ||
        item.game_code.toLowerCase() === gameCode.toLowerCase()
      );
    },
    // 为IG彩票游戏分配不同的图片代码
    getIGGameCode(item, index) {
      // 如果有特定的game_code且在映射表中存在，直接使用
      if (item.game_code && ['XGC', 'GFC', 'SSC', 'xgc', 'gfc', 'ssc'].includes(item.game_code)) {
        return item.game_code;
      }

      // 根据索引或其他规则分配不同的图片
      const igGameCodes = ['XGC', 'GFC', 'SSC']; // 香港彩、官方彩、时时彩
      const assignedCode = igGameCodes[index % igGameCodes.length];

      console.log('IG彩票游戏分配 - index:', index, 'assignedCode:', assignedCode, 'originalGameCode:', item.game_code);

      return assignedCode;
    },
    // 获取彩票游戏的图片代码（统一处理所有彩票游戏）
    getLotteryImageCode(item, index) {
      console.log('彩票游戏处理 - platform_name:', item.platform_name, 'game_code:', item.game_code, 'index:', index);

      // 处理IG平台的各种变体（IG, IG5等）
      if (item.platform_name === 'IG' || item.platform_name === 'IG5') {
        const igCode = this.getIGGameCode(item, index);
        console.log('IG彩票最终代码:', igCode);
        return igCode;
      }

      return item.platform_name;
    },
    getGameImageSrc(category, platformName) {
      // 调试信息：输出实际的参数值
      if (category === 'lottery') {
        console.log('彩票游戏图片映射 - category:', category, 'platformName:', platformName);
      }

      // 图片文件名映射表 - 将大写的platform_name映射到实际存在的小写文件名
      const imageMapping = {
        realbet: {
          'AG': 'ag',          // AG平台使用ag图片
          'BBIN': 'bbin',      // BBIN平台使用bbin图片
          'WM': 'wm',          // WM平台使用wm图片
          'BG': 'bg',          // BG平台使用bg图片
          'DG': 'dg',          // DG平台使用dg图片
          'AB': 'allbet',      // AB平台使用allbet图片
          'YB': 'qg',          // YB平台使用qg图片（避免与AG重复）
          'CQ9': 'obgzr',      // CQ9平台使用obgzr图片（避免与AG重复）
          'EVO': 'videogame'   // EVO平台使用videogame图片（避免与AG重复）
        },
        sport: {
          'SBO': 'xsbo',       // SBO平台使用xsbo图片
          'IBC': 'sbtest',     // IBC平台使用sbtest图片
          'BBIN': 'bbin',      // BBIN平台使用bbin图片
          'SS': 'saba',        // SS平台使用saba图片（避免重复）
          'CMD': 'cmd',        // CMD平台使用cmd图片
          'FB': 'hbb',         // FB平台使用hbb图片（避免重复）
          'XJ': 'oap',         // XJ平台使用oap图片（避免重复）
          'HG': 'obgty',       // HG平台使用obgty图片（避免重复）
          'AI': 'saba',        // AI平台使用saba图片（如果SS不存在的话）
          'NEWBB': 'hbb'       // NEWBB平台使用hbb图片（如果FB不存在的话）
        },
        gaming: {
          'IA': 'ia',          // IA平台使用ia图片
          'TFG': 'ld',         // TFG平台使用ld图片
          'IM': 'avia',        // IM平台使用avia图片（避免与IA重复）
          'OBG': 'obgdj'       // OBG平台使用obgdj图片
        },
        joker: {
          'BBCARD': 'bbin',    // BBCARD平台使用bbin图片
          'KY': 'kx',          // KY平台使用kx图片
          'BL': 'bg',          // BL平台使用bg图片
          'GDQ': 'datqp',      // GDQ平台使用datqp图片（避免与KX重复）
          'TH': 'fg',          // TH平台使用fg图片（避免与KX重复）
          'LEG': 'leg',        // LEG平台使用leg图片
          'KX': 'kx2',         // KX平台使用kx2图片（避免与KY重复）
          'NW': 'hlgame',      // NW平台使用hlgame图片（避免与KX重复）
          'WALI': 'ly',        // WALI平台使用ly图片（避免与KX重复）
          'MT': 'sy',          // MT平台使用sy图片（避免与KX重复）
          'LG': 'vg',          // LG平台使用vg图片（避免与LEG重复）
          'OBG': 'obgqp',      // OBG平台使用obgqp图片
          'XSJ': 'xsj'         // XSJ平台使用xsj图片
        },
        lottery: {
          'VR': 'vrbet',       // VR平台使用vrbet图片
          'AECP': 'dl',        // AECP平台使用dl图片（避免与BBIN重复）
          'IG': 'imone',       // IG平台使用imone图片（避免与BBIN重复）
          'IG5': 'ig5',        // IG5平台默认使用ig5图片（将被动态替换）
          // IG彩票子游戏映射 - 根据实际game_code值映射到对应图片
          'XGC': 'XGC',        // IG香港彩使用XGC图片
          'GFC': 'GFC',        // IG官方彩使用GFC图片
          'SSC': 'SSC',        // IG时时彩使用SSC图片
          'xgc': 'XGC',        // IG香港彩小写版本
          'gfc': 'GFC',        // IG官方彩小写版本
          'ssc': 'SSC',        // IG时时彩小写版本
          'SGWIN': 'yb',       // SGWIN平台使用yb图片（避免与jz重复）
          'TCG': 'tc',         // TCG平台使用tc图片
          'BBIN': 'bbin',      // BBIN平台使用bbin图片
          'OBCP': 'obgcp',     // OBCP平台使用obgcp图片
          'LOTTERY': 'LOTTERY', // LOTTERY平台使用LOTTERY图片
          'LOTTO': 'LOTTO',    // LOTTO平台使用LOTTO图片
          'YB': 'yb'           // YB平台使用yb图片
        },
        concise: {
          'AG': 'ag',          // AG平台使用ag图片（修复：原来错误映射到ae）
          'AT': 'at',          // AT平台使用at图片（修复：原来错误映射到ae）
          'JOKER': 'jdb',      // JOKER平台使用jdb图片
          'CQ9': 'cg',         // CQ9平台使用cg图片
          'JDB': 'jdb',        // JDB平台使用jdb图片
          'PG': 'pg',          // PG平台使用pg图片
          'PP': 'pp',          // PP平台使用pp图片
          'PT': 'ps',          // PT平台使用ps图片（已在数据层排除）
          'MG': 'bg',          // MG平台使用bg图片（已在数据层排除）
          'PNG': 'png',        // PNG平台使用bng图片
          'FC': 'hc',          // FC平台使用hc图片
          'MW': 'mw',          // MW平台使用sg图片
          'SG': 'sg'           // SG平台使用sg图片
        },
        fishing: {
          'JDB': 'jdb',        // JDB捕鱼使用fishing文件夹中的jdb图片
          'CQ9': 'cq9',        // CQ9捕鱼使用jdb图片（暂时使用同一图片）
          'LEG': 'leg',        // LEG捕鱼使用jdb图片（暂时使用同一图片）
          'KY': 'ky',         // KY捕鱼使用jdb图片（暂时使用同一图片）
          'AT': 'ai',         // AT捕鱼使用jdb图片（暂时使用同一图片）
          'AGBY': 'agby',       // AGBY捕鱼使用jdb图片（暂时使用同一图片）
          'BBIN': 'bbin'        // BBIN捕鱼使用jdb图片（暂时使用同一图片）
        }
      };

      // 获取映射的文件名，如果没有映射则使用小写版本
      const mappedName = imageMapping[category] && imageMapping[category][platformName]
        ? imageMapping[category][platformName]
        : platformName.toLowerCase();

      // 调试信息：输出最终的图片路径
      if (category === 'lottery') {
        console.log('最终图片路径:', `/static/image/${category}/${mappedName}.png`);
      }

      return `/static/image/${category}/${mappedName}.png`;
    },
    openGogao(val) {
      this.goInfo = val;
    },
    changtanshow() {
      this.tanshow = !this.tanshow;
    },
    changGameType(type) {
      this.gameType = type;
    },
    doCopy(msg) {
      let cInput = document.createElement('input');
      cInput.style.opacity = '0';
      cInput.value = msg;
      document.body.appendChild(cInput);
      // 选取文本框内容
      cInput.select();

      // 执行浏览器复制命令
      // 复制命令会将当前选中的内容复制到剪切板中（这里就是创建的input标签）
      // Input要在正常的编辑状态下原生复制方法才会生效
      document.execCommand('copy');

      // 复制成功后再将构造的标签 移除
      this.$parent.showTost(1, '复制成功！');
    },
    changleftshow() {
      this.leftshow = !this.leftshow;
    },
    getBanList() {
      let that = this;
      that.$parent.showLoading();
      that.$apiFun
        .post('/api/bannerList', { type: 2 })
        .then(res => {
          if (res.code != 200) {
            that.showTost(0, res.message);
          }
          if (res.code == 200) {
            that.bannerList = res.data;
          }
          that.$parent.hideLoading();
        })
        .catch(res => {
          that.$parent.hideLoading();
        });
    },
    homenotice() {
      let that = this;
      that.$apiFun.post('/api/homenotice', {}).then(res => {
        if (res.code != 200) {
          that.showTost(0, res.message);
        }
        if (res.code == 200) {
          that.homenoticelis = res.data;
          that.ok = true;
        }
      });
    },
    onChange(index) {
      this.current = index;
    },
    changhongbashow() {
      this.hongbashow = false;
    },
    changappShow() {
      this.appShow = false;
    },
  },
  mounted() {
    let that = this;
  },
  updated() {
    let that = this;
  },
  beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
@import '../../../static/css/2d87bbdbffeb4734e5c7.css';

/* 隐藏滚动条 */
.mode {
  overflow-x: hidden !important;
  max-width: 100vw !important;
}

/* 全局隐藏滚动条 */
:global(html) {
  overflow: hidden !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
}

:global(body) {
  overflow: hidden !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
}

:global(*) {
  box-sizing: border-box;
}

/* 隐藏滚动条但保持滚动功能 */
:global(html::-webkit-scrollbar),
:global(body::-webkit-scrollbar) {
  display: none;
}

:global(html),
:global(body) {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.domainModal_content__1nBgc {
  overflow: auto;
}
// 轮播样式
.swiper-dots {
  display: flex;
  position: absolute;
  left: 40px;
  bottom: 10px;
  width: 48px;
  height: 24px;
  background-image: url(data:image/png;base64,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);
  background-size: 100% 100%;
}
.swiper-dots .num {
  width: 24px;
  height: 24px;
  border-radius: 50px;
  font-size: 16px;
  color: #fff;
  text-align: center;
  line-height: 24px;
}
.swiper-dots .sign {
  position: absolute;
  top: 0;
  left: 50%;
  line-height: 24px;
  font-size: 8px;
  color: #fff;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

// 新的头部导航样式
.header-nav {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;

  .header-logo {
    height: 35px;
    width: 35px;
    flex: 1;
    display: flex;
    justify-content: flex-start;
  }

  .header-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    flex: 1;
    text-align: center;
  }

  .header-actions {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;

    .download-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      color: white;
      padding: 6px 12px;
      border-radius: 15px;
      cursor: pointer;
      font-size: 12px;
      transition: background-color 0.2s ease;
      width: 82px;
      height: 26px;
      img {
        height: 26px;
        width: 82px;
      }

      &:hover {
        background: #7C3AED;
      }
    }

    .sound-btn {
      font-size: 18px;
      cursor: pointer;
      padding: 4px;
      color: #666;

      &:hover {
        color: #8B5CF6;
      }
    }
  }
}

// 主页头部标题
.homeHeder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  display: flex;
  box-sizing: border-box;
  padding: 0 30px;
  z-index: 999;
  align-items: center;
  justify-content: space-between;
  .leftImg {
    width: 19px;
    height: 19px;
  }
  .rbox {
    display: flex;
    height: 100%;
    align-items: center;
    font-size: 0.24rem;
    font-family: PingFangSC-Regular, sans-serif;
    img {
      width: 32px;
      height: 32px;
    }
  }
}
.leftbox {
  width: 7.5rem;
  height: 100%;
  background-color: white;
  color: #666;
  .side__main__1NhyG {
    box-sizing: border-box;
    padding: 0 20px;
    h3 {
      font-size: 20px;
      font-weight: 400;
      margin: 0;
      padding-top: 72px;
    }
    .side__vip__1dW8w {
      background: url(/static/style/sidebr_vip_card.1ce7485811699286f87aae1827de7acf.png) no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 20px;
      color: #fff;
      position: relative;
      p {
        color: hsla(0, 0%, 100%, 0.6);
        font-size: 12px;
        margin: 5px 0 0 0;
      }
      .topxs {
        font-size: 16px;
      }
      dd {
        float: right;
        border: 0.02rem solid #fff;
        border-radius: 0.24rem;
        height: 0.48rem;
        line-height: 0.48rem;
        width: 1.88rem;
        text-align: center;
        font-size: 10px;
        position: absolute;
        top: 20px;
        right: 20px;
      }
    }
  }
  ul {
    list-style: none;
    margin-top: 0.36rem;
    li {
      display: block;
      line-height: 0.96rem;
      height: 0.96rem;
      border-bottom: 0.02rem solid #e6ebf6;
      color: #4e6693;
      font-size: 0.28rem;
      padding: 0 0.14rem;
      img {
        width: 0.36rem;
        vertical-align: middle;
        margin: -0.04rem 0.24rem 0 0;
      }
      span {
        float: right;
      }
    }
  }
  .nisd {
    position: absolute;
    width: 4.72rem;
    height: 0.8rem;
    line-height: 0.8rem;
    left: 0.9rem;
    bottom: 1rem;
    background: #dfe5ff;
    border-radius: 0.4rem;
    border: 0;
    color: #4e6693;
    font-size: 0.28rem;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      vertical-align: middle;
      margin: -0.04rem 0.08rem 0 -0.08rem;
      width: 0.32rem;
    }
  }
}
.gonggao {
  width: 90%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.caosuo {
  color: #666;
  width: 90%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .lefsg {
    flex: 1;
    .login-register-area {
      display: flex;
      flex-direction: column;
      gap: 8px;
      .auth-buttons {
        display: flex;
        gap: 8px;
        .login-btn, .register-btn {
          height: 24px;
          width: 54px;
          cursor: pointer;
          transition: all 0.3s ease;
          border: none;
          background: none;
          &:hover {
            transform: scale(1.05);
            opacity: 0.8;
          }
        }
      }
      .auth-text {
        font-size: 11px;
        color: #999;
      }
    }
    .name {
      font-size: 12px;
    }
    .mey {
      margin-top: 3px;
      font-size: 16px;
      font-weight: 700;
      span {
        font-size: 10px;
        font-weight: 400;
      }
    }
  }
  .riggs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .lisImg {
      width: 56px;
      text-align: center;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      img {
        width: 60%;
        display: block;
        margin: 0 auto;
      }
    }
  }
}
.xiding {
  background-color: white;
  padding: 12px 0;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

// 游戏
.gameBoxs {
  background: white;
  -webkit-box-shadow: inset 0 0.08rem 0.16rem rgba(0,0,0,0.05);
  box-shadow: inset 0 0.08rem 0.16rem rgba(0,0,0,0.05);
  box-sizing: border-box;
  padding: 10px;
  color: #666;
  height: 518px;
  display: flex;
  flex-direction: column;
  margin-bottom:70px;
  .lfst {
    width: 100%;
    height: 70px;
    background: transparent;
    border-radius: 0;
    border: none;
    display: flex;
    justify-content: space-around;
    align-items: center;
    .ls.active {
      color: #fff;
    }
    .ls {
      text-align: center;
      border-radius: 50%;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 52px;
      transition: all 0.2s ease;
      margin: 0 2px;
      img {
        width: 50px;
        height: 52px;
        display: block;
        margin: 0 auto 2px auto;
      }
      span {
        font-size: 8px;
        display: block;
        white-space: nowrap;
        font-weight: 400;
        color: #fff;
        line-height: 1;
      }
    }
  }
  .rigts {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    height: 440px;
    box-sizing: border-box;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
    .game-card {
      background: #fff;
      border-radius: 8px;
      padding: 0;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;
      overflow: hidden;
      cursor: pointer;
      width: 100%;
      flex-shrink: 0;
      &:hover {
        transform: scale(1.02);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
    .game-card img {
      width: 100%;
      height: 120px;
      border-radius: 8px;
      display: block;
    }
  }
}
// app下载
.appbox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 1.8rem;
  box-sizing: border-box;
  padding-right: 15px;
  .colse {
    width: 0.4rem;
    margin-left: 0.1rem;
  }
  .logo {
    width: 1rem;
    margin: 0 0.2rem;
  }
  .testz {
    flex: 1;
    .tit {
      font-size: 0.23rem;
      font-weight: 700;
    }
    .cx {
      font-size: 0.2rem;
      color: #ccc;
    }
  }
}
</style>
