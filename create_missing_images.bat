@echo off
echo Creating missing game images...

REM Create missing realbet images
if not exist "static\image\realbet\ab.png" copy "static\image\realbet\allbet.png" "static\image\realbet\ab.png"
if not exist "static\image\realbet\yb.png" copy "static\image\realbet\ag.png" "static\image\realbet\yb.png"
if not exist "static\image\realbet\cq9.png" copy "static\image\realbet\ag.png" "static\image\realbet\cq9.png"
if not exist "static\image\realbet\evo.png" copy "static\image\realbet\ag.png" "static\image\realbet\evo.png"

REM Create missing sport images
if not exist "static\image\sport\sbo.png" copy "static\image\sport\xsbo.png" "static\image\sport\sbo.png"
if not exist "static\image\sport\ibc.png" copy "static\image\sport\saba.png" "static\image\sport\ibc.png"
if not exist "static\image\sport\ss.png" copy "static\image\sport\oap.png" "static\image\sport\ss.png"
if not exist "static\image\sport\fb.png" copy "static\image\sport\bbin.png" "static\image\sport\fb.png"
if not exist "static\image\sport\xj.png" copy "static\image\sport\bbin.png" "static\image\sport\xj.png"
if not exist "static\image\sport\hg.png" copy "static\image\sport\hbb.png" "static\image\sport\hg.png"
if not exist "static\image\sport\ai.png" copy "static\image\sport\bbin.png" "static\image\sport\ai.png"
if not exist "static\image\sport\newbb.png" copy "static\image\sport\bbin.png" "static\image\sport\newbb.png"

REM Create missing gaming images
if not exist "static\image\gaming\tfg.png" copy "static\image\gaming\ld.png" "static\image\gaming\tfg.png"
if not exist "static\image\gaming\im.png" copy "static\image\gaming\ia.png" "static\image\gaming\im.png"

REM Create missing joker images
if not exist "static\image\joker\bbcard.png" copy "static\image\joker\bbin.png" "static\image\joker\bbcard.png"
if not exist "static\image\joker\ky.png" copy "static\image\joker\kx.png" "static\image\joker\ky.png"
if not exist "static\image\joker\bl.png" copy "static\image\joker\leg.png" "static\image\joker\bl.png"
if not exist "static\image\joker\gdq.png" copy "static\image\joker\leg.png" "static\image\joker\gdq.png"
if not exist "static\image\joker\th.png" copy "static\image\joker\leg.png" "static\image\joker\th.png"
if not exist "static\image\joker\nw.png" copy "static\image\joker\xsj.png" "static\image\joker\nw.png"
if not exist "static\image\joker\wali.png" copy "static\image\joker\vg.png" "static\image\joker\wali.png"
if not exist "static\image\joker\mt.png" copy "static\image\joker\sy.png" "static\image\joker\mt.png"
if not exist "static\image\joker\lg.png" copy "static\image\joker\ly.png" "static\image\joker\lg.png"

REM Create missing lottery images
if not exist "static\image\lottery\vr.png" copy "static\image\lottery\vrbet.png" "static\image\lottery\vr.png"
if not exist "static\image\lottery\aecp.png" copy "static\image\lottery\bbin.png" "static\image\lottery\aecp.png"
if not exist "static\image\lottery\ig.png" copy "static\image\lottery\bbin.png" "static\image\lottery\ig.png"
if not exist "static\image\lottery\sgwin.png" copy "static\image\lottery\bbin.png" "static\image\lottery\sgwin.png"
if not exist "static\image\lottery\tcg.png" copy "static\image\lottery\tc.png" "static\image\lottery\tcg.png"
if not exist "static\image\lottery\obcp.png" copy "static\image\lottery\obgcp.png" "static\image\lottery\obcp.png"

REM Create missing concise images
if not exist "static\image\concise\ag.png" copy "static\image\concise\bbin.png" "static\image\concise\ag.png"
if not exist "static\image\concise\at.png" copy "static\image\concise\bbin.png" "static\image\concise\at.png"
if not exist "static\image\concise\joker.png" copy "static\image\concise\jdb.png" "static\image\concise\joker.png"
if not exist "static\image\concise\cq9.png" copy "static\image\concise\bbin.png" "static\image\concise\cq9.png"
if not exist "static\image\concise\png.png" copy "static\image\concise\pg.png" "static\image\concise\png.png"
if not exist "static\image\concise\mg.png" copy "static\image\concise\bbin.png" "static\image\concise\mg.png"
if not exist "static\image\concise\pt.png" copy "static\image\concise\pp.png" "static\image\concise\pt.png"
if not exist "static\image\concise\fc.png" copy "static\image\concise\bbin.png" "static\image\concise\fc.png"
if not exist "static\image\concise\mw.png" copy "static\image\concise\bbin.png" "static\image\concise\mw.png"

echo Done! All missing images have been created.
pause
