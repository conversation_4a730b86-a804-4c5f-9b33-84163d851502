const fs = require('fs');

// 真人游戏 - 创建大写版本
console.log('创建真人游戏大写版本...');
try {
  fs.copyFileSync('static/image/realbet/ag.png', 'static/image/realbet/AG.png');
  console.log('✓ AG.png');
} catch (e) { console.log('✗ AG.png:', e.message); }

try {
  fs.copyFileSync('static/image/realbet/bbin.png', 'static/image/realbet/BBIN.png');
  console.log('✓ BBIN.png');
} catch (e) { console.log('✗ BBIN.png:', e.message); }

try {
  fs.copyFileSync('static/image/realbet/wm.png', 'static/image/realbet/WM.png');
  console.log('✓ WM.png');
} catch (e) { console.log('✗ WM.png:', e.message); }

try {
  fs.copyFileSync('static/image/realbet/bg.png', 'static/image/realbet/BG.png');
  console.log('✓ BG.png');
} catch (e) { console.log('✗ BG.png:', e.message); }

try {
  fs.copyFileSync('static/image/realbet/dg.png', 'static/image/realbet/DG.png');
  console.log('✓ DG.png');
} catch (e) { console.log('✗ DG.png:', e.message); }

try {
  fs.copyFileSync('static/image/realbet/allbet.png', 'static/image/realbet/AB.png');
  console.log('✓ AB.png');
} catch (e) { console.log('✗ AB.png:', e.message); }

// 创建缺失的图片
try {
  fs.copyFileSync('static/image/realbet/ag.png', 'static/image/realbet/YB.png');
  console.log('✓ YB.png');
} catch (e) { console.log('✗ YB.png:', e.message); }

try {
  fs.copyFileSync('static/image/realbet/ag.png', 'static/image/realbet/CQ9.png');
  console.log('✓ CQ9.png');
} catch (e) { console.log('✗ CQ9.png:', e.message); }

try {
  fs.copyFileSync('static/image/realbet/ag.png', 'static/image/realbet/EVO.png');
  console.log('✓ EVO.png');
} catch (e) { console.log('✗ EVO.png:', e.message); }

console.log('\n真人游戏图片创建完成！');
