const fs = require('fs');
const path = require('path');

// 复制文件的函数
function copyFile(source, target) {
  try {
    if (fs.existsSync(source)) {
      fs.copyFileSync(source, target);
      console.log(`✓ 复制成功: ${target}`);
    } else {
      console.log(`✗ 源文件不存在: ${source}`);
    }
  } catch (error) {
    console.log(`✗ 复制失败: ${target} - ${error.message}`);
  }
}

// 首先创建大写版本的现有文件
console.log('创建大写版本的现有文件...');
// 真人游戏
copyFile('static/image/realbet/ag.png', 'static/image/realbet/AG.png');
copyFile('static/image/realbet/allbet.png', 'static/image/realbet/ALLBET.png');
copyFile('static/image/realbet/bbin.png', 'static/image/realbet/BBIN.png');
copyFile('static/image/realbet/bg.png', 'static/image/realbet/BG.png');
copyFile('static/image/realbet/dg.png', 'static/image/realbet/DG.png');
copyFile('static/image/realbet/wm.png', 'static/image/realbet/WM.png');

// 真人游戏缺失的图片（使用大写文件名）
console.log('\n创建真人游戏缺失的图片...');
copyFile('static/image/realbet/allbet.png', 'static/image/realbet/AB.png');
copyFile('static/image/realbet/ag.png', 'static/image/realbet/YB.png');
copyFile('static/image/realbet/ag.png', 'static/image/realbet/CQ9.png');
copyFile('static/image/realbet/ag.png', 'static/image/realbet/EVO.png');

// 体育游戏大写版本
console.log('\n创建体育游戏大写版本...');
copyFile('static/image/sport/bbin.png', 'static/image/sport/BBIN.png');
copyFile('static/image/sport/xsbo.png', 'static/image/sport/SBO.png');
copyFile('static/image/sport/sbtest.png', 'static/image/sport/IBC.png');
copyFile('static/image/sport/bbin.png', 'static/image/sport/SS.png');
copyFile('static/image/sport/bbin.png', 'static/image/sport/FB.png');
copyFile('static/image/sport/bbin.png', 'static/image/sport/XJ.png');
copyFile('static/image/sport/bbin.png', 'static/image/sport/HG.png');
copyFile('static/image/sport/bbin.png', 'static/image/sport/AI.png');
copyFile('static/image/sport/bbin.png', 'static/image/sport/NEWBB.png');
copyFile('static/image/sport/cmd.png', 'static/image/sport/CMD.png');

// 电竞游戏大写版本
console.log('\n创建电竞游戏大写版本...');
copyFile('static/image/gaming/ia.png', 'static/image/gaming/IA.png');
copyFile('static/image/gaming/ld.png', 'static/image/gaming/TFG.png');
copyFile('static/image/gaming/ia.png', 'static/image/gaming/IM.png');

console.log('\n所有图片创建完成！');
