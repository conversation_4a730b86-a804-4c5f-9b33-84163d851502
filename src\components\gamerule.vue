<template>
  <div class="body">
    <div data-v-f531b812="" class="app app-ti_green">
      <div data-v-8a75a126="" data-v-f531b812="" class="header">
        <div data-v-8a75a126="" class="header__top-wrapper">
          <div data-v-8a75a126="" class="van-nav-bar van-nav-bar--fixed fixed-top nav-header">
            <div class="van-nav-bar__content">
              <div class="van-nav-bar__left" @click="$router.back()">
                <i class="van-icon van-icon-arrow-left van-nav-bar__arrow"></i>
              </div>
              <div class="van-nav-bar__title van-ellipsis">游戏规则</div>
            </div>
          </div>
        </div>
      </div>
      <van-tabs>
        <van-tab title="体育游戏规则">
          <div data-v-4fda7660="" data-v-3424411f="" class="sports-games sporga_green">
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">一般体育说明&nbsp;/&nbsp;概述</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  此规则与条款将适用于本公司所有的投注种类； 会员们有责任确保您获知所有的规则与条款，我们保留随时修改条款的权利，并且会将修改的内容公布在本网站上，公布在网站公告上的信息可作为投注附加的规则与条款，若有任何差异或矛盾的地方，将以附加信息为准；
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">本公司对此条款视为公平公正的，若您有任何意见或疑问，您可以联络我们的客服部，我们的客服团队将热诚协助每位客户，并确保能及时友善的解决您的问题。对于任何错误或争论，我们的客服团队将竭力提供服务；</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">所有的条款用于在会员与公司之间建立一般的原则。对产生的任何争议，希望通过该条款让双方都得到满意的解决方案。</div>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">一般体育规则</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">所有在本公司进行的投注都需要依照以下规则与条款处理。在个别体育项目里注明的规则将视为体育主要规则:</div>
                <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__ol">
                  <li data-v-4fda7660="" data-v-7b40e3b3="">1.所有投注项目的最高和最低投注额将由公司决定，如有任何更改无需提前通知。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">2.会员申请账号时需提供正确的个人信息，本公司对提供伪造或错误信息的账号将不负任何责任。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">3.会员将全权负责账号提交的所有交易。在投注前请仔细检查选项，一旦投注提交成功后，将无法更改或取消。公司对会员自身原因造成的遗漏或重复投注不负任何责任。会员可以在"交易记录"中查看详情确保所有提交的注单已成功。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">4.在任何投诉中，如果在公司的数据库中没有存储任何记录，公司将不接受也不认可任何会员提供的复印件或单据。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">5.公司保留在任何时候关闭或冻结会员账号的权利。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">6.公司保留在任何时候暂停/中止会员对任何盘口进行投注的权利。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">7.公司保留对已预先知道赛果的投注作出取消的权利。如果由于"滚球现场"延迟而引起盘口的赔率错误，此期间的注单将视为无效。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">8.赛事时间,计时器和红牌等信息仅供会员参考，公司对提供此信息的准确性不负任何责任。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">
                    9.如果比赛或赛事取消，中断或延迟并且没有在官方指定开球时间的36小时内重新开始，所有该场赛事的投注即被视为无效且取消，除非在个别体育规则里另有指定注明。某些无条件终止的盘口将会相应地结算。单独的体育规则中对此类盘口的结算程序做了说明。公司取消该赛事所有注单的结果被视为最终决定，无需参考官方赛事裁判或相关部门的决定。连串投注将会继续按照注单剩余赛事的赛果结算，该取消赛事的赔率将会按照1倍计算。
                  </li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">10.如果比赛或是赛事取消，所有该场赛事的投注即被视为无效且取消，除非在个别体育规则里另有指定注明。对于取消的定义和处理本公司拥有最终解释权。 11.如果对其它语言版本的信息或球赛队名有争议，请以英文网站的名称为准。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">
                    12.由以下事件造成的任何损失，公司不赋予任何责任:
                    <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__sub-ol">
                      <li data-v-4fda7660="" data-v-7b40e3b3="">a.公司的网站、服务器或网络中断。</li>
                      <li data-v-4fda7660="" data-v-7b40e3b3="">b.公司数据库、服务器丢失信息或信息遭受破坏。</li>
                      <li data-v-4fda7660="" data-v-7b40e3b3="">c.不法分子攻击网站、服务器或网络供应商。</li>
                      <li data-v-4fda7660="" data-v-7b40e3b3="">d.进入网站时由于网络供应商原因造成的网络缓慢。</li>
                    </ol>
                  </li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">13.如果对此规则与条款的内容有任何疑义，请以公司的解释为准。</li>
                </ol>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">赛果与派彩</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__ol">
                  <li data-v-4fda7660="" data-v-7b40e3b3="">1.赛果均在赛事结束后判定，除非在个别体育规则里另有注明。赛果公布72小时后，若对任何赛果有争议，本公司将不认可。在赛果公布72小时内，除了任何体育纪律委员会所重新裁决之赛果，本公司只会修正人为、系统或参考赛果的相关网页失误等原因的错误。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">2.投注通常在赛事结束后派彩。但考虑到会员的利益，某些投注会在官方公布赛事结果之前就进行派彩。这些投注会在无条件确定结果的情况下派彩，可以包括被放弃，暂停或推迟的赛事。为此，公司保留对此而造成的错误进行更改的权利。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">3.派彩是根据官方来源或相关体育权威机构判定的结果为准。所有的交易将以公司最新备份数据记录为准，公司不接纳任何投诉或争议，除非会员提供交易记录的截图或影印证据，否则公司的数据记录将作为最终证明。</li>
                </ol>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">滚球类型投注规则</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">滚球投注是指对正在进行比赛的赛事进行投注。注单会在赛事进行比赛后开始接收并且在盘口关盘后停止所有交易。个别体育会开出多个滚球种类的盘口供投注。</div>
                <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__ol">
                  <li data-v-4fda7660="" data-v-7b40e3b3="">1.所有滚球投注均需要遵守系统验收程序。这可能会导致每笔投注延迟确认或出现投注失败的情况。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">
                    2.所有在注单上显示“待确认”的字眼则表示注单正在等待系统的判断投注是否成功。这表示注单在等待被确认或可能被取消。在赛事有事件发生或出现其他特殊情况时，所有待确认中的注单将不被确认并视为投注失败。此事件可包含进球，红卡，点球或技术上问题等等。(其他例子并不一一详尽列出)。
                  </li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">3.当有重大事件发生时，会应用电视裁判助理（VAR）来判定，这可能会导致之前已确定的注单被取消。注单被取消始于重大事件发生的开端直到VAR的最终官方判定已宣布。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">
                    4.在滚球投注中，本公司需强调以下条款，确保投注是按照正确的时间、金额和在正确的情况下进行:
                    <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__sub-ol">
                      <li data-v-4fda7660="" data-v-7b40e3b3="">a.比赛赛果和入球时间以本公司网站公布的为准，我们不参考任何其它官方网站，体育网站，或"即时比分"等网站公布的赛果或入球时间。</li>
                      <li data-v-4fda7660="" data-v-7b40e3b3="">b.如果有合理的理由怀疑投注是在比赛时某个事件发生后才提交的，本公司将保留取消此注单且不需提供任何理由和证明的权利。</li>
                      <li data-v-4fda7660="" data-v-7b40e3b3="">c.如果出现网站无法更新比分、赔率或盘口的情况，本公司保留权利取消所有未确认且处理中的注单。</li>
                    </ol>
                  </li>
                </ol>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">有关时间规则</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__ol">
                  <li data-v-4fda7660="" data-v-7b40e3b3="">1.如比赛在法定时间提前进行，在比赛开始前的投注依然有效，在比赛开始后的所有投注均视为无效(滚球投注另作别论)。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">2.足球球赛的正常完场时间包括任何球员受伤的补时。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">3.除非在个别体育规则另有注明，加时得分则不计算在正常完场时间内。</li>
                </ol>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">并列名次规则</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__ol">
                  <li data-v-4fda7660="" data-v-7b40e3b3="">1.一场赛事的盘口产生2名或者以上的获胜者，那么赢利金额将减少。计算方法是将本金除以获胜者人数，然后乘以原赔率。这部分本金及赢利将返还给会员，剩余的本金将输掉。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">2.例如:</li>
                  <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">第一阶段-您投注5英镑，赔率15/1（可能的赢利金额为75英镑）；</div>
                  <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">第二阶段-您投注的对象与另一名对象同时获胜；</div>
                  <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">第三阶段-您本金的一半将会乘以原赔率计算，剩下的部分将输掉。因此2.5英镑乘以15/1=37.50英镑+2.5英镑的返还的本金就是总赢利；</div>
                  <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">如果有2名以上的获胜者，注金将会分成相应的等份</div>
                  <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">实际上在2名获胜者的情况下，本金的一半（50%）将乘以原赔率，剩余的一半（50%）将输掉。</div>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">3.多冠军规则并不始终适用，如果我们最终决定赛事的冠军可由获得的比分决定。（例如小组赛事）</li>
                </ol>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">场地变更</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__ol">
                  <li data-v-4fda7660="" data-v-7b40e3b3="">1.如果比赛场地有变更（主客队调换），所有此注单将被取消。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">2.本公司保留权利取消因更换场地而可能对赛事结果有影响的注单，例如：网球比赛更换场地表面。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">3.若比赛原定在中立场进行改为在非中立场进行且在本公司判定下对比赛没有影响，注单将继续保持有效。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">4.在个别体育项目里若有特别注明将覆盖以上规则。</li>
                </ol>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">错误</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__ol">
                  <li data-v-4fda7660="" data-v-7b40e3b3="">
                    1.本公司力求错误发生的机率保持最低，但若有注单显然是在盘口有错误的情况下提交，我们将保留取消此注单的权利。错误的情况包括：
                    <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__sub-ol">
                      <li data-v-4fda7660="" data-v-7b40e3b3="">a.赔率错误（和市场上提供的有明显差别）；</li>
                      <li data-v-4fda7660="" data-v-7b40e3b3="">b.盘口信息错误，例如：让球数，大小数等；；</li>
                      <li data-v-4fda7660="" data-v-7b40e3b3="">c.赛事信息错误，例如：参赛队名或队员，赛事日期或开赛时间。</li>
                    </ol>
                  </li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">2.若因遇到以上的情况而需取消任何注单，我们会尽可能的与客户取得联系，有关讯息也会及时在公告栏里发布。</li>
                </ol>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">异常投注行为</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">1.对任何怀疑在投注时涉嫌作弊或破坏本公司投注平台的会员，公司有权在毫无警告或通知下取消此会员所有的注单并且冻结账号。异常行为包括使用任何设备，自动设备，软件，程序等方式干涉本网站的运作。</div>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">接受更好的赔率</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  “术语”“接受更好的赔率”“即添加到该选项的所有投注单，只要在选择的时间所提供的可能性表现出比最初显示更高的回报，就可以自动进行处理。如果调整后的投注单盘口显示出比原先的返回值较低，那么该投注将不会被自动处理。”接受更好的赔率“这个功能可以通过勾选投注单中“选择“或是“取消选择“来开启或是关闭。
                </div>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">更高赔率/复式过关</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__ol">
                  <li data-v-4fda7660="" data-v-7b40e3b3="">1.本公司拥有提供以及随时撤回所有更高赔率/复式过关的权利。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">2.所有更高赔率投注/复式过关的限额，可能在任何时间进行调整。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">3.所有更高赔率/复式过关规则相同于一般规则及上述各体育规则中的规定。以足球为例--以足球为例-“曼联胜出及大/小2.5”将遵守“独赢盘和进球数大/小盘”足球规则。。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">4.足球的更高赔率/复式过关是计算在常规时间内，不包括额外时间或罚球（除非另有说明）。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">5.对于复式过关，如果您投注的项目在无条件确定结果的情况下，则该投注根据结果相应的结算为“赢”或“输”。以足球为例：如果您选择进球数超过2.5个，并且在暂停时比分是2：1，则您的投注将被视为“赢”</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">6.对于复式过关，如果您投注的项目在结果未确定时被暂停，则将视为无效投注。以足球为例：如果您选择的比分是2-2（全赛时），并且在暂停时比分为1-1，则您的投注将被视为无效。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">7.对于复式过关，单一项目或多个项目是平局或无效的情况下，赔率会重新调整。</li>
                </ol>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">冠军投注</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">足球冠军投注仅适用于常规赛季（除非另作说明）。整个赛季赛程结束后球队最终的排名来决定冠军。季后赛或随后查询(潜在扣分)各自联赛将不计算在最终赛果。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">联赛的最终结果，例如:&nbsp;世界杯冠军或F1车手冠军。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">在预赛中胜出，例如:&nbsp;世界杯小组冠军。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">在最后一场赛事中，例如:&nbsp;能否出线，能否踢加时，能否踢点球。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">比赛的最终结果，例如：F1赛事的个人冠军。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">最高得分数。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">最优秀的选手等。</div>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">一般规则</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <ol data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__ol">
                  <li data-v-4fda7660="" data-v-7b40e3b3="">所有冠军投注基于比赛的最终结果。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">无论挑选的人或球队是否有参与，所有冠军投注都视为有效。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">多冠军规则的结算方式将应用于冠军投注。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">无论在什么情况下，如果使用"其他球员"或"其他球队"代替名&nbsp;称的参赛者将被视为无名称。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">如果体育项目有开出特殊规则，则其将取代一般适用规则。</li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">
                    冠军项目月份榜首榜尾结算规则: 此项目计算方式于该月第一 日至最后一日（英国时间23:59），如遇最后一日赛事延赛或 赛事取消则不予计算，不影响该项目的计算。延迟或取消的赛事不予计算，如果由于人为错误或技术故障，赛果出来后盘口没有关闭，公司有权取消因此错误下注的注单。
                  </li>
                  <li data-v-4fda7660="" data-v-7b40e3b3="">足球冠军投注仅适用于常规赛季（除非另作说明）。整个赛季赛程结束后球队最终的排名来决定冠军。季后赛或随后查询(潜在扣分)各自联赛将不计算在最终赛果。</li>
                </ol>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">连串过关/复式过关/组合过关</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  连串过关是指选择二个或更多的赛事在一个单一的注单中。每一个选择连串投注的赛事必须获胜此连串的注单才视为获胜。如果第一个注单是获胜的，投注获胜的注单会添加到第二个投注选项，直到连串过关中的所有投注获胜或到有一场失败为结束。某些连串过关是组合几个不同的连串在一个单一的连串注单中。例如：4串11是11个不同的连串投注。请参考"连串过关投注类型"选择查看更多的信息。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  例如:<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  投注一个100￥的3串1连串过关在以下的三场赛事:<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  曼联@1.80@1.80 <br data-v-4fda7660="" data-v-7b40e3b3="" />切尔西@1.50 <br data-v-4fda7660="" data-v-7b40e3b3="" />阿森纳@1.66
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  如果所有的三场赛事都获胜，连串过关的盈利为448.20￥（包括本金）。详细的计算方式您可以查看以下的信息:<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  注单1：曼联1.80*￥100=返还￥180. <br data-v-4fda7660="" data-v-7b40e3b3="" />
                  注单2：切尔西1.50*￥180=返还￥270. <br data-v-4fda7660="" data-v-7b40e3b3="" />
                  注单3：阿森纳1.66*￥270=返还￥448.20.
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  连串过关注意事项:<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  本公司一个注单中最多的连串过关为10串。所有投注赛事都需要根据体育博彩规则为准。不是所有的赛事都可以投注连串过关。如果您在投注列表中看到不可以组合二个不相关的赛事（可以查看以下的信息关于有关联的连串），那么就是其中一场并没有开出连串过关投注。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">a.连串过关可以投注不同种类的赛事，以及不在同一天的比赛。</div>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">b.连串过关里的相关选择</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">c.连串过关投注，选择组合有关联的同一赛事或投注市场的结果会影响其他另一个投注市场，此注单是不接受的。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  例如:<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  以下的2串1是不接受的，由于是同一场赛事: <br data-v-4fda7660="" data-v-7b40e3b3="" />曼联获胜独赢盘口@1.80 <br data-v-4fda7660="" data-v-7b40e3b3="" />
                  曼联2-0获胜，正确比分盘口@7.0<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  如果曼联2-0获胜，组合盘口为12.6。其实盘口应该为7.0，因为曼联2-0获胜，那么独赢盘口自然而然为获胜。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">连串过关投注，选择组合有关联的同一球队或球员，即使他们是不同的时间，同样是不接受例如一个结果会影响另一个结果。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  例如:<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  曼联赢得冠军杯冠军@10 <br data-v-4fda7660="" data-v-7b40e3b3="" />曼联赢得冠军杯冠军@10 <br data-v-4fda7660="" data-v-7b40e3b3="" />组合盘口@60.0
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">这个连串过关被视为第二个赛果会影响到第一个赛果。如果曼联获得冠军杯联赛冠军，那么曼联自然而然就进入冠军杯决赛。因此，盘口仅仅为10.0。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">本公司有权利取消有关联的连串过关投注。</div>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">连串过关</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">在连串过关中有任何的投注赛事无效或者打和（如以下的范例），此连串过关注单仍然有效，并且按照胜出的其余投注结算，范例如下：</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  投注项1：切尔西（-0.5）-切尔西赢。<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  投注项2:曼联（-1）-曼联赢1-0.<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  投注项3:阿森纳（-0.5）-阿森纳赢。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  正如曼联是以（-1）的亚洲盘口1-0获胜，但在连串过关中的此赛事视为和。因此，当切尔西获胜连串阿森纳获胜过关，此连串过关将被视为切尔西以及阿森纳的2串，而非最初的3串。同时，打和的过关投注项目将会被自动以1计算。
                </div>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">过关的计算范例如下</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content-table__wrapper">
                  <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content-table__title">范例1：其中一个投注项为例:</div>
                  <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content-table__inner">
                    <table data-v-4fda7660="" data-v-7b40e3b3="">
                      <thead data-v-4fda7660="" data-v-7b40e3b3="">
                        <th data-v-4fda7660="" data-v-7b40e3b3="">投注项</th>
                        <th data-v-4fda7660="" data-v-7b40e3b3="">切尔西@1.50</th>
                        <th data-v-4fda7660="" data-v-7b40e3b3="">曼联@1.80</th>
                        <th data-v-4fda7660="" data-v-7b40e3b3="">阿森纳@1.66</th>
                      </thead>
                      <tbody data-v-4fda7660="" data-v-7b40e3b3="">
                        <tr data-v-4fda7660="" data-v-7b40e3b3="">
                          <td data-v-4fda7660="" data-v-7b40e3b3="">让球</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">(-0.5/1)</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">(-1)</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">(-1/1.5)</td>
                        </tr>
                        <tr data-v-4fda7660="" data-v-7b40e3b3="">
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赔率</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">1.85</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">1.95</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">2.05</td>
                        </tr>
                        <tr data-v-4fda7660="" data-v-7b40e3b3="">
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赛果</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赢2-0</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赢1-0</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赢3-0</td>
                        </tr>
                        <tr data-v-4fda7660="" data-v-7b40e3b3="">
                          <td data-v-4fda7660="" data-v-7b40e3b3="">结果</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赢半/平半</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">全赢</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">全赢</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">连串投注：￥100 3串一</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">计算方式如下：<br data-v-4fda7660="" data-v-7b40e3b3="" />$100x[1+0.5x0.85]x1.95x2.05=$569.64，扣除本金=赢$469.64</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">切尔西(-0.5/1)：赢半/平半–此注单被分为两项，只有一半的投注盈利，</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">盈利的部分￥50x1.85=￥92.50</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">打和部分￥50x1=50</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">返还是￥92.50+￥50=￥142.50。此金额将移至下个投注项</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">曼联（-1）:盈利-142.50x1.95=￥277.87，此金额将移至下个投注项</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">阿森纳(-1/-1.5):盈利-277.87x2.05=￥569.64</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">总盈利:￥569.64-￥100=￥469.64.</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content-table__wrapper">
                  <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content-table__title">范例3：其中一个投注项为输半/平半</div>
                  <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content-table__inner">
                    <table data-v-4fda7660="" data-v-7b40e3b3="">
                      <thead data-v-4fda7660="" data-v-7b40e3b3="">
                        <th data-v-4fda7660="" data-v-7b40e3b3="">投注项</th>
                        <th data-v-4fda7660="" data-v-7b40e3b3="">切尔西@1.50</th>
                        <th data-v-4fda7660="" data-v-7b40e3b3="">曼联@1.80</th>
                        <th data-v-4fda7660="" data-v-7b40e3b3="">阿森纳@1.66</th>
                      </thead>
                      <tbody data-v-4fda7660="" data-v-7b40e3b3="">
                        <tr data-v-4fda7660="" data-v-7b40e3b3="">
                          <td data-v-4fda7660="" data-v-7b40e3b3="">让球</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">(-0.5/1)</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">(-1)</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">(-1/1.5)</td>
                        </tr>
                        <tr data-v-4fda7660="" data-v-7b40e3b3="">
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赔率</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">1.85</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">1.95</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">2.05</td>
                        </tr>
                        <tr data-v-4fda7660="" data-v-7b40e3b3="">
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赛果</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赢2-0</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赢1-0</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赢3-0</td>
                        </tr>
                        <tr data-v-4fda7660="" data-v-7b40e3b3="">
                          <td data-v-4fda7660="" data-v-7b40e3b3="">结果</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">赢半/平半</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">全赢</td>
                          <td data-v-4fda7660="" data-v-7b40e3b3="">全赢</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">连串投注：￥100 3串一</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">计算方式如下：<br data-v-4fda7660="" data-v-7b40e3b3="" />$100x[1+0.5x0.85]x1.95x2.05=$569.64，扣除本金=赢$469.64</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">切尔西(-0.5/1)：赢半/平半–此注单被分为两项，只有一半的投注盈利，</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">盈利的部分￥50x1.85=￥92.50</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">打和部分￥50x1=50</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">返还是￥92.50+￥50=￥142.50。此金额将移至下个投注项</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">曼联（-1）:盈利-142.50x1.95=￥277.87，此金额将移至下个投注项</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">阿森纳(-1/-1.5):盈利-277.87x2.05=￥569.64</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">总盈利:￥569.64-￥100=￥469.64.</div>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">连串投注种类</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">本公司提供多种多样的连串过关投注。以下的表格可以清楚的查看每一种连串过关的排列组合。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  双式投注/2串1<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  一个复式投注中包括2项不同的赛事。每个选择的赛事必须胜出方为赢。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  三式投注/3串1 <br data-v-4fda7660="" data-v-7b40e3b3="" />
                  一个三式投注中包括3项不同的赛事。每个选择的赛事必须胜出方为赢。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  复式累计连串<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  一个累计连串投注必须4个或更多不同的选项。所有的选项投注必须获胜方为赢。4个累计连串的投注可以参考4串1/4项复式投注；5个累计连串的投注可以参考5串1/5项复式投注，直到10串1/10项复式投注。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  3串4投注 <br data-v-4fda7660="" data-v-7b40e3b3="" />
                  3串4是由3个不同赛事组成的4个不同的赛事，可以看作为4个分开的注单，它包括3个双式投注和1个三式投注。最少其中的2场赛事需要获胜，才会保证获得一些派彩。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  4串11投注<br data-v-4fda7660="" data-v-7b40e3b3="" />4串11是由4个不同赛事组成的11个不同的赛事，可以看作为11个分开的注单，它包括6个双式投注，4个三式投注，1个4串1组成。最少其中的2场赛事需要获胜，才会保证获得一些派彩。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  5串26投注<br data-v-4fda7660="" data-v-7b40e3b3="" />5串26是由5个不同赛事组成的26个不同的投注，可以看作为26个分开的注单，它包括10个双式投注，10个三式投注，5个4串1投注以及1个5串1组成。最少其中的2场赛事需要获胜，才会保证获得一些派彩。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  6串57投注 <br data-v-4fda7660="" data-v-7b40e3b3="" />6串57是由6个不同赛事组成的57个不同的投注，可以看作为57个分开的注单，它包括15个双式投注，20个三式投注，15个4串1投注，6个5串1投注以及一个6串1组成。最少其中的2场赛事需要获胜，才会保证获得一些派彩。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  7串120投注<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  7串120是由7个不同赛事组成的120个不同的投注，可以看作为120个分开的注单，它包括21个双式投注，35个三式投注，35个4串1，21个5串1，7个6串1以及1个7串1组成。最少其中的2场赛事需要获胜，才会保证获得一些派彩。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  8串247投注<br data-v-4fda7660="" data-v-7b40e3b3="" />8串247是由8个不同的赛事组成的247个不同的投注，可以看作为247个分开的注单，它包括28个双式投注，56个三式投注，70个4串1，56个5串1，28个6串1，8个7串1以及1个8串1组成。最少其中的2场赛事需要获胜，才会保证获得一些派彩。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  9串502投注<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  9串502是由9个不同的选项组成的502个不同的投注，可以看作为502个分开的注单。它包括36个双式投注，84个三式投注，126个4串1，126个5串1，84个6串1，36个7串1，9个8串1以及1个9串1组成。最少其中的2场赛事需要获胜，才会保证获得一些派彩。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  10串1013投注<br
                    data-v-4fda7660=""
                    data-v-7b40e3b3=""
                  />10串1013是由10个不同的选项组成的1013个不同的投注，可以看作为1013个分开的注单。它包括45个双式投注，120个三式投注，210个4串1，252个5串1，210个6串1，120个7串1，45个8串1，10个9串1以及1个10串1。最少其中的2场赛事需要获胜，才会保证获得一些派彩。
                </div>
              </div>
            </div>
            <div data-v-4fda7660="" class="sports-games-article">
              <div data-v-32de3775="" data-v-4fda7660="" class="main-container-title">提前结算规则</div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">一般规则</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">1、提前结算只适用于指定的赛事及盘口类型。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">2、提前结算功能可能随时被取消或者是被调整，不是一直都存在的。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">3、提前结算所获得的金额是不可修改的，KG体育也不会对这个金额做任何的解释。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  4、对于部分提前结算，注单的提前结算部分和未提前结算部分的金额都需要不小于该种投注类型的最小投注额。<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  •例1-最小投注限额=1.00/客户投注=100.00，投注则可在1.00-99.00范围内结束投注。<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  •例2-最小投注限额=1.00/客户投注=5.00，投注则可在1.00-4.00范围内结束投注。<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  •例3-最小投注限额=1.00/客户投注=1.00，注单无法进行部分提前结算，但是注单是可以进行全部提前结算的。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  5、所有注单在提前结算时显示的金额就是提前结算成功后将会返回到您账户的金额。这包含了与提前结算金额相关的投注本金。<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  •例1-如果结束投注金额显示为1500.00，而相关投注金额为1000.00，则已确认的赢/亏为+500.00(赢)<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  •例2-如果结束投注金额显示的是800.00，而相关投注金额为1000.00，则确认的赢/亏为-200.00(亏)
                </div>
              </div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">结算规则</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">1、如果赛事被取消或延期，本公司保留取消及撤销先前已提前结算的投注的权利，除非盘口已无条件确定。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">2、如果赛事被中断，且未能在36小时内继续，本公司保留取消及撤销先前已提前结算的投注的权利，除非盘口已无条件决定。</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">3、如果已提前结算的投注被取消，本公司保留取消该投注及收回提前结算资金的权利。</div>
              </div>
              <div data-v-7b40e3b3="" data-v-4fda7660="" class="main-container-content">
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">结算实例</div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  1、单式投注–全部提前结算<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  如果某赛事在其单式注单全部提前结算后被取消，那么提前结算的金额将退还给本公司，投注本金将退回给客户。然而，如果已提前结算的注单是投注在特定时间段(如足球上半场或篮球第一节)，而赛事是在完成这一时间段后被取消的，那么提前结算为有效。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  2、单式投注–部分提前结算<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  如果某赛事在其单式注单部分提前结算后被取消，那么提前结算的金额将退还给本公司，部分提前结算的投注本金将退回给客户。剩余的投注金额（不包含最初的部分提前结算金额）也是无效的，并会退回到客户的账户。然而，如果已提前结算的注单是投注在特定时间段(如足球上半场或篮球第一节)，而赛事是在完成这一时间段后被取消的，那么提前结算和剩余的部分均有效。
                </div>
                <div data-v-4fda7660="" data-v-7b40e3b3="" class="main-container-content__text">
                  3、复式投注-当所选赛事被取消时<br data-v-4fda7660="" data-v-7b40e3b3="" />
                  如果复式注单中的一场或者多场赛事被取消，那么任何之前的提前结算将视为无效，除非盘口已无条件确定。对于单式连串注单，所有剩余的选项将会以原始的赔率进行计算。例如：5串1的单式连串注单中（投注了A,B,C,D,E五场赛事的连串）而B赛事被取消了，那么注单将会以剩下的ACDE结算。如果剩余的赛事还没有结束，则可能出现再次提前结算的功能。但是如果剩余的赛事都已经结束，那么注单也会相应的被结算。
                </div>
              </div>
            </div>
          </div>
        </van-tab>
        <van-tab title="真人娱乐规则">
          <div data-v-1598c97e="" data-v-3424411f="" class="immortal-games immoga_green">
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">轮盘简介</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">
                  轮盘不仅仅是一个简单的娱乐场游戏，它也是一款最有活力的游戏。今天我们了解的第一种形式的轮盘是在18世纪法国，距今已经有很长的时间。轮盘的投注方式多样，{{$store.state.appInfo.title}}平台的真人荷官轮盘代表着顶尖的经典娱乐场游戏。由于这些原因，轮盘成为在线玩家首选的游戏之一，我们真人娱乐场目标是给玩家提供最好的在线轮盘体验。
                </div>
              </div>
            </div>
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">怎样玩轮盘</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">轮盘以转轴为中心，转轴上有数字小方框，数字所组成的队列和组合有着不同的变化。在适用的情况下，所有数字格也代表红和黑，不包括0的颜色为绿色。轮盘是放置在投注区域的旁边。</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">
                  玩家可以选择投注在他们感觉能赢的数字，数字组合或颜色。轮盘的桌面是由一个荷官（有时更传统的也可称之为"赌台主持人"）来负责旋转小球和处理投注。玩家可以操作投注直到荷官讲出"无法再投注"。游戏开始时，荷官转动轮盘，小球会向逆时针方向旋转滚动，最终会停在其中一个小方格内。然后公布获胜号码，荷官支付给获胜者以及收集所有输的投注。一个新的回合将会在所有注单处理完毕后马上开始。
                </div>
              </div>
            </div>
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">欧洲轮盘规则</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">
                  欧洲轮盘是历史最悠久，最著名一个游戏版本。欧洲轮盘包含37个号码，从数字1号至36号，颜色为红色和黑色，37个号码中包括绿色的0号。轮盘局部位置上的队列可能随机出现，但是实际上是根据数学方式安排以确保每个数字都有相同的获胜机会。轮盘的桌面可以进行投注直到荷官宣布"无法再投注"为止。
                </div>
              </div>
            </div>
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">百家乐简介</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">
                  可以很肯定的说百家乐是非常受欢迎的纸牌游戏，对于真实的起源地有一些不同的说法。一些说法是百家乐源于意大利，另一些说法是百家乐源于法国。我们可以确认的是今天我们所了解的最受欢迎的百家乐有三种：PuntoBanco（可称为"北美百家乐"），百家乐ChemindeFer，和百家乐Banque。北美百家乐完全是一个碰运气及没有技术性或策略性的游戏。每个玩家的行动强迫性的是由玩家的牌去决定的没有其它的选择性的。百家乐ChemindeFer和Banque，相比之下，两者的玩家都可以选择，在游戏的过程中会用到技巧的。百家乐是一个对比两手牌的纸牌游戏，即"闲家"和"庄家"。每个百家乐都会有三种结果："闲家"（闲家获得最高分数），"庄家"，和局"。
                </div>
              </div>
            </div>
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">怎么玩百家乐</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">
                  百家乐游戏中使用8副（416张）扑克。2到9点纸牌的点数与其牌面数字相同，A的点数为1，J,Q,K和10的点数为0。每手牌的实际点数是以所有牌数的总数为准，计算的方式是所有牌的点数加在一起。例如：一手牌为4，Q，和10，点数计算公式为（4+0+0=4）而一手牌是A和9那么它将计算为0点（1+9＝0）。
                </div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">游戏将会在所有玩家投注"闲家"，"庄家"，或"和"后开始。一些桌面玩家可以投注更多的选项，例如闲对子和庄对子。</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">每一个游戏回合，闲家和庄家都将会获得二张牌。如果闲家和庄家的前二张牌已经为8点或9点，不会出现第三张牌。</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">手牌的点数将会以二张牌总数个位数计算。例如，如果二张牌是一个8和7，总点数为15点，那么计算的点数就是5点。牌的点数的范围是0至9，不可能会爆牌。</div>
              </div>
            </div>
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">第三张牌规则</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">第三张牌是否会补是取决于闲家和庄家的点数，规则如下：</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">
                  如果闲家总点数为0,1,2,3,4或5点，闲家需要补第三张牌<br data-v-1598c97e="" data-v-7b40e3b3="" />如果闲家总点数为6或7，闲家停牌<br data-v-1598c97e="" data-v-7b40e3b3="" />如果闲家没有补牌，庄家总点数为0,1,2,3,4或5点，庄家需要补第三张牌<br
                    data-v-1598c97e=""
                    data-v-7b40e3b3=""
                  />如果庄家总点数为6或7，庄家停牌。
                </div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">支付赔率如下:</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">闲家：1赔1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;庄家：1赔0.95</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">和局：1赔8（闲家和庄家投注将会返回）</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">闲/庄对（前二张牌成为一对）：1赔11</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">大（游戏中牌的总数超过4张）：1赔0.54</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">小（游戏中牌的总数是4张）：1赔1.5</div>
              </div>
            </div>
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">免佣百家乐百家乐</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">超6百家乐，也称为Punto 2000，是百家乐的一个变种形式，和传统百家乐的赔付方式有一些不同。就像一般的百家乐，也是选择筹码投注在庄家，闲家或和局。真人荷官将会根据正常百家乐第三张的规则发牌。</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">
                  如果您的押中正确的庄家，闲家或和局，您就获胜。闲家1赔1或平局1赔8都是和正常的百家乐派彩一致，但是庄家的派彩是不同的，一般的百家乐是1赔0.95，而超6百家乐庄家赔付是1赔1，除非庄家以牌面6点胜出，那么赔付的比率为1赔0.5。
                </div>
              </div>
            </div>
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">免佣百家乐百家乐</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">骰宝，也称为幸运骰子或买大小，骰宝是一种古老的中国骰子游戏。字面意思骰宝是指"珍贵的骰子"，买大小的意思是"大或小"。游戏是使用三个骰子和一个桌面上投注区域组成，每一个区域代表着不同的骰子结果或组合。</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">
                  骰宝游戏历史可以追溯到上百年，很久很久以前据说人们是用砖头代替骰子。这些砖头骰子被染上符号，在碗里摇来摇去，然后掷到地上查看符合或数字的组合。玩家猜测正确的符合或数字。随着时间的推移，砖头骰子变成现在的骰子，同时骰宝游戏通过网络传递到全世界，成为今天最受欢迎的游戏之一。
                </div>
              </div>
            </div>
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">骰宝简介</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">
                  游戏是使用三个骰子放在骰盅内，所有的投注将被接受后荷官将摇晃骰盅。所有玩家需要预测游戏的结果。如果结果无法确认，例如：骰子侧立面朝侧面，那么无法确认的游戏赛果将被公告无效，骰子将重新转动。出现全围（所有骰子显示相同的数字）庄家通杀大/小和单/双。
                </div>
              </div>
            </div>
            <div data-v-1598c97e="" class="immortal-games-article">
              <div data-v-32de3775="" data-v-1598c97e="" class="main-container-title">怎样玩骰宝</div>
              <div data-v-7b40e3b3="" data-v-1598c97e="" class="main-container-content">
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">小： 总点数为4至10（若开出围骰投注算输</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">大： 总点数为11至17（若开出围骰投注算输）</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">单： 总点数为任何单数总和（若开出围骰投注算输）</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">双：总点数为任何双数总和（若开出围骰投注算输）</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">双骰： 投注在任何特定对子（例如一对1），如果出现选择的2个相同的点数玩家获胜。</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">全围： 出现任何三个点数即获胜。</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">单一骰子： 投注在任何特定的点数1至6，如果选择的点数出现一次，赔付1赔1，如果选择的点数出现二次，赔付1赔2，如果选择的点数出现三次，赔付1赔3。</div>
                <div data-v-1598c97e="" data-v-7b40e3b3="" class="main-container-content__text">两骰组合： 投注在任何组合的1至6点数。三个骰子必须出现两个选择的点数。</div>
              </div>
            </div>
          </div>
        </van-tab>
        <van-tab title="电子游戏规则">
          <div data-v-26503bcd="" data-v-3424411f="" class="electron-games elecga_green">
            <div data-v-26503bcd="" class="electron-games-article">
              <div data-v-32de3775="" data-v-26503bcd="" class="main-container-title">老虎机规则</div>
              <div data-v-7b40e3b3="" data-v-26503bcd="" class="main-container-content">
                <div data-v-26503bcd="" data-v-7b40e3b3="" class="main-container-content__text">
                  老虎机是一种很常见的游戏，它最大的特色就是可以让您以小博大。若能掌握老虎机的相关的策略与技巧，老虎机可说是一种是低投资，高收益的神奇致富机器。现今老虎机不仅运营在传统公司，还可以选择在线体验老虎机的无穷乐趣。{{$store.state.appInfo.title}}娱乐场也将竭尽全力给您提供世界上最好的老虎机游戏。
                </div>
                <div data-v-26503bcd="" data-v-7b40e3b3="" class="main-container-content__text">{{$store.state.appInfo.title}}娱乐场老虎机配有最新3D引擎制作的炫丽页面，以及激昂愉悦的游戏音效，震撼您的视觉感官。所有游戏操作简单，界面简洁清新。3D场景震撼无比，游戏内容精彩绝伦。</div>
                <div data-v-26503bcd="" data-v-7b40e3b3="" class="main-container-content__text">
                  无论您选择哪一种老虎机游戏，投注的结果都是由随机数产生器决定的。按照支付比例（也就是总彩金数和总投注额之比）派彩，执行每月一次的审计工作。所有审计都是由外国第三方国际认可的的财务公司进行独立操作。所有支付都会在{{$store.state.appInfo.title}}娱乐场页面发布。我们郑重承诺：{{$store.state.appInfo.title}}平台所提供的在线老虎机游戏赛果是绝对公平公正的。
                </div>
                <div data-v-26503bcd="" data-v-7b40e3b3="" class="main-container-content__text">玩老虎机最重要的是要有耐心，要能够控制自己，更需要时间和赌金的坚持才能收获丰厚的果实。希望您在{{$store.state.appInfo.title}}娱乐场收获游戏快乐的同时也能获得意外的惊喜大奖!</div>
              </div>
            </div>
          </div>
        </van-tab>

        <van-tab title="彩票投注规则">
          <div data-v-087b1780="" data-v-3424411f="" class="lottery-games lotga_green">
            <div data-v-32de3775="" data-v-087b1780="" class="main-container-title big-title">时时彩玩法规则</div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">两面玩法</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content">
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  大小<br data-v-087b1780="" data-v-7b40e3b3="" />
                  开奖结果万位、千位、百位、十位或个位数字为5、6、7、8、9时为“大”，若为0、1、2、3、4时为“小”，当投注位数大小与开奖结果的位数大小相符时，即为中奖。※举例：投注者购买百位小，当期开奖结果如为20352（3为小），则视为中奖。
                </div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  单双<br data-v-087b1780="" data-v-7b40e3b3="" />
                  开奖结果万位、千位、百位、十位或个位数字为1、3、5、7、9时为“单”，若为0、2、4、6、8时为“双”，当投注位数单双与开奖结果的位数单双相符时，即为中奖。※举例:投注者购买百位单，当期开奖结果如为20130（1为单），则视为中奖。
                </div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">总和</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  总和总大：开奖结果5个号码加起的总和，大于等于23为总大。※举例：投注者购买总和大，当期开奖结果如为总数为28，则视为中奖。总小：开奖结果5个号码加起的总和，小于等于22为总小。※举例：投注者购买总和小，当期开奖结果如为总数为18，则视为中奖。总单：开奖结果5个号码加起的总和为单数。※举例：投注者购买总和单，当期开奖结果如为总数为29，则视为中奖。总双：开奖结果5个号码加起的总和为双数。※举例：投注者购买总和双，当期开奖结果如为总数为18，则视为中奖。
                </div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">总小：开奖结果5个号码加起的总和，小于等于22为总小。※举例：投注者购买总和小，当期开奖结果如为总数为18，则视为中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">总单：开奖结果5个号码加起的总和为单数。※举例：投注者购买总和单，当期开奖结果如为总数为29，则视为中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">总双：开奖结果5个号码加起的总和为双数。※举例：投注者购买总和双，当期开奖结果如为总数为18，则视为中奖。</div>
              </div>
            </div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">龙虎</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content">
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">龙：开出之号码第一球(万位)的中奖号码大于第五球(个位)的中奖号码，如出和局为打和。如：第一球开出4，第五球开出2；第一球开出9，第五球开出8；第一球开出5，第五球开出1，中奖为龙。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">虎：开出之号码第一球(万位)的中奖号码小于第五球(个位)的中奖号码，如出和局为打和。如：第一球开出7，第五球开出9；第一球开出3，第五球开出5；第一球开出5，第五球开出8，中奖为虎。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">和：开出之号码第一球(万位)的中奖号码等于第五球(个位)的中奖号码如：2***2则投注和局者视为中奖，其他视为不中奖。</div>
              </div>
            </div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">趣味玩法</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content">
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">豹子：三个位置对应的中奖号码都相同。如：中奖号码为000、111、999等，则投注豹子者视为中奖，其它视为不中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">顺子：三个位置对应的中奖号码数字相连（不分顺序）。如：中奖号码为012、123、234、345等，则投注顺子者视为中奖，其它视为不中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">对子：三个位置对应的中奖号码中有且仅有两个号码相同（不包括豹子）。如：中奖号码为001，112、696等，则投注对子者视为中奖，其它视为不中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">杂六：不包括豹子、对子、顺子的其他中奖号码。如：中奖号码为157，中奖号码位数之间无关联性，则投注杂六者视为中奖，其它视为不中奖。</div>
              </div>
            </div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">数字盘玩法</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content"><div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">于万千百十个任选一位，自0~9任选1个号进行投注，当开奖结果与所选的定位与号码相同且顺序一致时，即为中奖。</div></div>
            </div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">龙虎斗玩法</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content">
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">龙虎游戏规则：龙虎是以开奖结果的五个数字作为基准，取任意位置（万、千、百、十、个）的数字进行组合大小比对的一种玩法；当投注龙/虎时，开奖结果为和局，那么押注龙/虎视为不中奖；</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">当投注龙/虎时，开奖结果为和局，那么押注龙/虎视为不中奖；</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">当投注"和"时，开奖结果为龙/虎，投注“和”视为不中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">举例：开奖结果为：2,1,3,5,2万为龙、千为龙虎时：结果龙(2）大于虎（1），即为开龙；如万为龙，个为虎时，结果一样大，即为开和局。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">说明：龙1VS虎2即为万为龙，千为虎；龙2VS虎4即为千为龙，十为虎。</div>
              </div>
            </div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title big-title">PK10玩法规则</div>
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">单码/双面规则说明</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content">
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  定位<br data-v-087b1780="" data-v-7b40e3b3="" />指冠军、亚军、季军、第四、第五、第六、第七、第八、第九、第十名出现的顺序与号码为派彩依据 如第一个开奖号码为3号，投注冠军为3则视为中奖，其它号码视为不中奖。
                </div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">大小</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">开出的号码大于或等于6为大，小于或等于5为小。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">单双</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">号码为双数叫双，如4、6；号码为单数叫单，如3、5。</div>
              </div>
            </div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">龙虎</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content">
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">冠军龙虎 <br data-v-087b1780="" data-v-7b40e3b3="" />* 龙：冠军号码大于第十名号码视为“龙”中奖，如冠军开出07，第十名开出03。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">*虎：冠军号码小于第十名号码视为“虎”中奖，如冠军开出03，第十名开出07。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  亚军龙虎<br data-v-087b1780="" data-v-7b40e3b3="" />
                  *龙：亚军号码大于第九名号码视为“龙”中奖，如亚军开出07，第九名开出03。<br data-v-087b1780="" data-v-7b40e3b3="" />
                </div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">*虎：亚军号码小于第九名号码视为“虎”中奖，如亚军开出03，第九名开出07。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  季军龙虎 <br data-v-087b1780="" data-v-7b40e3b3="" />
                  * 龙：季军号码大于第八名号码视为“龙”中奖，如季军开出07，第八名开出03。
                </div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">*虎：季军号码小于第八名号码视为“虎”中奖，如季军开出03，第八名开出07。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">第四名龙虎 <br data-v-087b1780="" data-v-7b40e3b3="" />* *龙：第四名号码大于第七名号码视为“龙”中奖，如第四名开出07，第七名开出03。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">* 虎：第四名号码小于第七名号码视为“虎”中奖，如第四名开出03，第七名开出07。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  第五名龙虎<br data-v-087b1780="" data-v-7b40e3b3="" />
                  *龙：第五名号码大于第六名号码视为“龙”中奖，如第五名开出07，第六名开出03。
                </div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">*虎：第五名号码小于第六名号码视为“虎”中奖，如第五名开出03，第六名开出07。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">冠亚军和<br data-v-087b1780="" data-v-7b40e3b3="" />冠军号码与亚军号码的和值区间为3~19，当投注组合符合冠亚军和值，即视为中奖</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">冠亚军和大小<br data-v-087b1780="" data-v-7b40e3b3="" />当开奖结果冠军号码与亚军号码的和值大于11为大，投注“和大”则视为中奖；小于等于11为小，投注“和小”则视为中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  冠亚军和单双 <br data-v-087b1780="" data-v-7b40e3b3="" />
                  当开奖结果冠军号码与亚军号码的和值为单数如9、13，投注“和单”则视为中奖；为双数如12、16，投注“和双”则视为中奖。
                </div>
              </div>
            </div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title big-title">六合彩规则</div>
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">特码玩法</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content">
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">当期开出的最后一位号码为特码。当开出特码与投注号码一致、即视为中奖（其余情形视为不中奖）。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">双面 <br data-v-087b1780="" data-v-7b40e3b3="" />对特码的大、小、单、双等形态进行投注，当开出特码对应形态与投注内容一致，即视为中奖（其余情形视为不中奖）。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">大小：开出之特码大于或等于25为特码大，小于或等于24为特码小，开出49为和。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">单双：特码为双数叫特双，如8、16；特码为单数叫特单，如21、35，开出49为和。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">和大小：以特码个位和十位数字总和来判断胜负，和数大于或等于7为大，小于或等于6为小，开出49为和。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">和单双：以特码个位和十位数字总和来判断单双，如01，12，32为和单，02，11，33为和双，开出49为和。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">尾数大小：以特码个位数小于等于4时为尾小，大于等于5时为尾大；如01、32、44为特尾小；如05、18、19为特尾大，开出49为和。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">尾数<br data-v-087b1780="" data-v-7b40e3b3="" />对特码的个位数（即尾数）进行投注。当开出特码的尾数与投注内容一致，即视为中奖（其余情形视为不中奖）。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">1尾：01,11,21,31,41</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">2尾：02,12,22,32,42</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">3尾：03,13,23,33,43</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">4尾：04,14,24,34,44</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">5尾：05,15,25,35,45</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">6尾：06,16,26,36,46</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">7尾：07,17,27,37,47</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">8尾：08,18,28,38,48</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">9尾：09,19,29,39,49</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">0尾：10,20,30,40</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">例如：开奖结果特别号码是21则"1"尾为中奖，其他尾数都不中奖</div>
              </div>
            </div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">生肖玩法</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content">
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">生肖顺序为 鼠 &gt;牛 &gt;虎 &gt;兔 &gt;龙 &gt;蛇 &gt;马 &gt;羊 &gt;猴 &gt;鸡 &gt;狗 &gt;猪 。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">如今年是狗年，就以狗为开始，依顺序将49个号码分为12个生肖(如下)，再以生肖下注。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">狗01,13,25,37,49</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">猪12,24,36,48</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">鼠11,23,35,47</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">牛10,22,34,46</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">虎09,21,33,45</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">兔08,20,32,44</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">龙07,19,31,43</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">蛇06,18,30,42</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">马05,17,29,41</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">羊04,16,28,40</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">猴03,15,27,39</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">鸡02,14,26,38</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">特肖<br data-v-087b1780="" data-v-7b40e3b3="" />当期开奖的特别号，落在下注生肖范围内，视为中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  一肖<br data-v-087b1780="" data-v-7b40e3b3="" />
                  当期开奖的全部号码，包含前6个号码及特别号，只要其中1个号码在下注的生肖范围即算中奖。不论同生肖的号码出现一个或多个，派彩只派一次。
                </div>
              </div>
            </div>
            <div data-v-087b1780="" class="lottery-games-article">
              <div data-v-32de3775="" data-v-087b1780="" class="main-container-title">生肖玩法</div>
              <div data-v-7b40e3b3="" data-v-087b1780="" class="main-container-content">
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">
                  五不中<br data-v-087b1780="" data-v-7b40e3b3="" />挑选5个号码为一投注组合进行下注，若5个号码中没有当期开奖的所有7个号码中的任意一个，即视为中奖（其余情形视为不中奖）。<br data-v-087b1780="" data-v-7b40e3b3="" />
                  例如如开奖号码是：06,07,08,09,10,11,12，若你投的是01,02,03,04,05则中奖，而投01,02,03,04,06则不中奖。
                </div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">六不中<br data-v-087b1780="" data-v-7b40e3b3="" />例如开奖号码是：06,07,08,09,10,11,12，若你投的是01,02,03,04,05,13则中奖，而投01,02,03,04,06,13则不中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">七不中<br data-v-087b1780="" data-v-7b40e3b3="" />例如开奖号码是：06,07,08,09,10,11,12，若你投的是01,02,03,04,05,13,14则中奖，而投01,02,03,04,06,13,14则不中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">八不中<br data-v-087b1780="" data-v-7b40e3b3="" />例如开奖号码是：06,07,08,09,10,11,12，若你投的是01,02,03,04,05,13,14,15则中奖，而投01,02,03,04,06,13,14,15则不中奖。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">九不中<br data-v-087b1780="" data-v-7b40e3b3="" />例如开奖号码是：06,07,08,09,10,11,12，若你投的是01,02,03,04,05,13,14,15,16则中奖，而投01,02,03,04,06,13,14,15,16则不中。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">对所有七个开奖号码的数值总和的大、小、单、双等形态进行投注。当所有开奖号码和值对应形态与投注内容一致，即视为中奖（其余情形视为不中奖）。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">总和大小：所有七个开奖号码的分数总和大于或等于175为总和大；分数总和小于或等于174为总和小。如开奖号码为01、07、15、29、38、46、24，分数总和是160，为总和小。</div>
                <div data-v-087b1780="" data-v-7b40e3b3="" class="main-container-content__text">总和单双：所有七个开奖号码的分数总和是单数叫总和单(总单)，如分数总和是103、193；分数总和是双数叫总和双(总双)，如分数总和是108、160。</div>
              </div>
            </div>
          </div>
        </van-tab>
        <van-tab title="" disabled> </van-tab>
        <van-tab title="" disabled> </van-tab>
      </van-tabs>

      <div data-v-f531b812="" class="float-divbox"></div>
      <span data-v-7b0f8a3e="" data-v-f531b812="" class="customer-service-container"></span><span data-v-f531b812=""></span>
      <div data-v-55ec3770="" data-v-f531b812="" class="select-service-line-view select-service-line-view">
        <dl data-v-55ec3770="" class="select-service-list"><div data-v-55ec3770="" style="height: 55px"></div></dl>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'gamerule',
  data() {
    return {
      url: null,
    };
  },
  created() {
    let that = this;
  },
  methods: {},
  mounted() {
    let that = this;
  },
  updated() {
    let that = this;
  },
};
</script>

<style lang="scss" scoped>
@import '../../static/css/sports-games.f8d23fff.css';
.body {
  background: #f6f7f8 !important;
  width: 100%;
  min-height: 100vh;
}
.van-tabs .van-tab__pane {
  padding: 0.33333rem 0.4rem 0.26667rem;
}
</style>
