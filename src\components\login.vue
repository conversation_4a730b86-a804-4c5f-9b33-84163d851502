<template>
  <div class="login-container">
    <div class="login-adv">
        <img src="/static/image/loginBg.png" alt="" class="adv-img">
        <div class="close-btn" @click="closePage"></div>
    </div>

    <!-- 登录表单 -->
    <div class="form-container" v-if="infoType === 0">
      <div class="input-group">
        <input type="text" v-model="loginInfo.name" placeholder="请输入账号">
      </div>
      <div class="input-group">
        <input :type="psw1 ? 'password' : 'text'" v-model="loginInfo.password" placeholder="请输入密码">
        <div class="psw-see" @click="changPsw('psw1')"></div>
      </div>

      <div class="options">
        <div class="auto-login">
          <span :class="['select-box', { selected: autoLogin }]" @click="toggleAutoLogin"></span>
          自动登录
        </div>

      </div>
      <button class="login-btn" @click="login" :disabled="isLogging">
        {{ isLogging ? '登录中...' : '登录账号' }}
      </button>
      <div class="bottom-links">
        <a href="#" class="browse-link" @click="closePage">先去逛逛</a>
        <a href="#" class="register-link" @click="changInfoType(1)">注册账号</a>
      </div>
    </div>

    <!-- 注册表单 -->
    <div class="form-container" v-if="infoType === 1">
      <div class="input-group">
        <input type="text" v-model="registerInfo.name" placeholder="请输入账号">
      </div>
      <div class="input-group">
        <input :type="psw2 ? 'password' : 'text'" v-model="registerInfo.password" placeholder="请输入密码">
         <div class="psw-see" @click="changPsw('psw2')"></div>
      </div>
      <div class="input-group">
        <input :type="psw3 ? 'password' : 'text'" v-model="registerInfo.confirmPass" placeholder="请再次输入密码">
         <div class="psw-see" @click="changPsw('psw3')"></div>
      </div>
      <div class="input-group">
        <input type="text" v-model="registerInfo.realname" placeholder="请输入真实姓名">
      </div>
      <div class="input-group">
        <input :type="psw4 ? 'password' : 'text'" v-model="registerInfo.paypassword" placeholder="请输入支付密码">
        <div class="psw-see" @click="changPsw('psw4')"></div>
      </div>
      <div class="input-group captcha-group">
        <input type="text" v-model="registerInfo.captcha" placeholder="请输入验证码">
        <div class="captcha-image">
          <img :src="captchaUrl" alt="验证码" @click="refreshCaptcha">
        </div>
      </div>
      <button class="register-btn" @click="register">注册账号</button>
      <div class="bottom-links">
        <a href="#" class="login-link" @click="changInfoType(0)">返回登录</a>
      </div>
    </div>

    <!-- 底部客服 -->
    <!-- <div class="customer-service" @click="$parent.openKefu">
      <img data-v-25acf74d="" style="width:15px ;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABACAMAAACUXCGWAAAAnFBMVEUAAAAfKFofKFofKFofJ1ofKFofKFofKFofJ1ofKFofKFkfKFofKFofKFoeKFofKFoeKFkfKFofKFofKFoeKFofKFoeJ1oeJ1kfKFoaJ1geKFodKFofJ1oeJlohKVgkJFUfKFofKFofJ1kfKFsfKFofKFohKVwbJ1ofKFofJ1oeJ1ofKFoeKFkfKFofJ1oeKFofKVwfKFofKFofKFoAmCbJAAAAM3RSTlMA9/Jx70Tammr7jOTe1b+2n8WwqZZ4SjgcE2QsJiEXB5FRPaODMA8Ky4hb6n5W0LkZ4NwDTu5yAAAC40lEQVRYw6WW2XaiQBBAi00dUVYFBBGIGjVGk0z9/79Nmi6QVenhvuT0CVy7q2sB+nn31pax1GX1Z2JYn9k7CBPMTBVrqMYsEFJojoQdSI42WPFmYC/mMM1u9fjlhbM6KMph5Sz/YMHq+7XDK0Khr267SoxuU70IjvfKcaEnv24JNEiyL/rn4bnDwhxb6wk4Rct95uDPSDPoReHBMaEXExnnGJ6wvZPl6VkseIGLjCl0EvIbhJfwHOg8c0Q/MIA9MiJos2gc9eUFTKDFOk+wZJjklGek0kp2ZBwHlxcyNlAnbQVkQFbOGxthOaQmwyXvEsvKUzsiCghwaL+xZBsRUNBW7lDhKBiRMlm28OCTrkYErXke1igWIAjLFaeSO+x8qajEQcTrY+njL5moRGFvPdrGB1v6ohKPvaXV4ioLT7gtk4S17jwBYeSaxGWSk/CkZZJLsS2Hz5lQzLFWeTc95Q4ZiYtYwhIT1hB0LMnE7pewi5Uu5VKx8kOZb8ADm/25JYEtkCuUWc4uybewhyvVb0S3PoxL3pKo6s4gUS1uhNpSmlcszaBlnjHrokHOhkrm3ZLvqmSTBV0dRGtLpr2S6IqS1tXgLQHJnl9cjcSk0dktocAyiVJWNalL/AkygqYkzSUqDaG4OultZBhvQMQp5nxUjyMX7e0OdzYsfNpt+ZZBGa28Bbvjh0vfjjcomLFlSknnwoE//YNM1lFfkoTEX785updn2h+LBVFLk1DFOqtTI9tKOQ34rm/CzVyuKJzypGXYCHmb59C55+t0E7p6fkr7c9s1jHOMmNZZ6u4VWjSIfT/qbuEZU1w1GIOGdJ0jiCT6BhiBT51wDB6/l2CMY83TMBqh2BnccRzhmPFU1rcwlKM1+WU5Ld/wKDPtzWBHVpahzxUmreeDFdXivALEyoQWC03k+PhgamLBJQEB5tiBK3izSsvwxxWuuKCh0OcxiBNWDIup978Fb6ryL2crfJaf/wAqssKiCg2VdQAAAABJRU5ErkJggg==" alt="" >
      <span>在线客服</span>
    </div> -->
  </div>
</template>

<script>
export default {
  name: 'login',
  data() {
    return {
      registerInfo: {},
      loginInfo: {},
      imgLis: ['2PYL', '6AQ5', '8PHD', '21I7', '69HM', 'ACWA', 'DUZ7', 'IY98', 'K647', 'M52T', 'NY52', 'NZFA', 'SN76', 'SP4D', 'VAEO', 'YFQM', 'ZZU5', '7GQT', 'LFW3', 'NU2T', 'UAE3'],
      index: 0,
      infoType: 0, //0 是登陆 1是注册
      psw1: true,
      psw2: true,
      psw3: true,
      psw4: true,
      pid: '',
      autoLogin: false,
      isLogging: false
    };
  },
  created() {
    let that = this;
    var query = that.$route.query;
    if (query.type) {
      that.infoType = query.type;
    }
    if (query.pid) {
      that.pid = query.pid;
    }
    that.changIndex();
  },
  methods: {
    closePage() {
      this.$router.push('/');
    },
    changPsw(name) {
      this[name] = !this[name];
    },
    changInfoType(type) {
      let that = this;
      if (that.infoType == type) {
        return;
      }
      that.infoType = type;
      that.changIndex();
      that.loginInfo = {};
      that.registerInfo = {};
      this.psw1 = true;
      this.psw2 = true;
      this.psw3 = true;
      this.psw4 = true;
    },
    changIndex() {
      this.index = parseInt(20 * Math.random());
    },
    register() {
      let that = this;
      let info = that.registerInfo;
      if (!info.name || info.name.length < 6) {
        that.$parent.showTost(0, ' 用户名长度6~16位，以字母或数字组合！');
        return;
      }
      if (!info.password || info.password.length < 6) {
        that.$parent.showTost(0, '请输入正确的密码长度，最少6位！');
        return;
      }
      if (!info.confirmPass || info.confirmPass != info.password) {
        that.$parent.showTost(0, '两次密码不一致！');
        return;
      }
      if (!info.realname || info.realname.length < 2) {
        that.$parent.showTost(0, '请输入您的真实姓名!');
        return;
      }
      if (!info.paypassword || info.paypassword.length < 6) {
        that.$parent.showTost(0, '请输入正确的支付密码长度，最少6位！');
        return;
      }
      if (!info.captcha) {
        that.$parent.showTost(0, '请输入验证码！');
        return;
      }
      that.$parent.showLoading();
      if (that.pid) {
        info.pid = that.pid;
      }
      that.$apiFun.register(info).then(res => {
        that.$parent.showTost(1, res.message);
        if (res.code == 200) {
          that.$router.push('/');
        }
        that.$parent.hideLoading();
      });
    },
    login() {
      let that = this;
      let info = that.loginInfo;

      if (that.isLogging) {
        return; // 防止重复点击
      }

      if (!info.name || !info.password) {
        that.$parent.showTost(0, '请输入您的账号和密码！');
        return;
      }


      that.isLogging = true;
      that.$parent.showLoading();
      that.$apiFun.login(info).then(res => {
        if (res.code !== 200) {
          that.$parent.showTost(0, res.message);
        }
        if (res.code === 200) {
          localStorage.setItem('token', res.data.api_token);
          sessionStorage.setItem('token', res.data.api_token);
          localStorage.setItem('userInfo', JSON.stringify(res.data));
          that.$store.commit('changToken');
          that.$store.commit('changUserInfo');
          that.$parent.openDaoTime();
          that.$router.push('/');
        }
        that.isLogging = false;
        that.$parent.hideLoading();
      }).catch(err => {
        that.isLogging = false;
        that.$parent.hideLoading();
        that.$parent.showTost(0, '登录失败，请重试！');
      });
    },

    toggleAutoLogin() {
      this.autoLogin = !this.autoLogin;
    },
  },
};
</script>

<style lang="scss" scoped>
.login-container {
    background-color: #fff;
    height: 100vh;
    overflow-y: auto;
}
.login-adv {
    position: relative;
    .adv-img {
        width: 100%;
        display: block;
    }
    .close-btn {
      width: 42px;
      height: 26px;
      background: url(/static/image/close.png) no-repeat 50%;
      background-size: 100%;
      position: absolute;
      top: 36px;
      right: 27px;
    }
}
.form-container {
    padding: 40px;
    border-radius: 15px 15px 0 0;
    margin-top: -95%;
    background-color: #fff;
    position: relative;
    z-index: 2;
    .input-group {
        height: 50px;
        border-radius: 25px;
        background-color: #f5f5f5;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        padding: 0 20px;
        input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 14px;
            outline: none;
        }
        .psw-see {
            width: 20px;
            height: 12px;
            background: url(/static/image/eye_close.png) no-repeat center;
            background-size: 100%;
        }
        &.captcha-group {
            .captcha-image {
                img {
                    height: 30px;
                }
            }
        }
    }
    .options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        font-size: 12px;
        color: #999;
        .auto-login {
            display: flex;
            align-items: center;
            .select-box {
                width: 15px;
                height: 15px;
                border-radius: 50%;
                border: 1px solid #ccc;
                margin-right: 5px;
                &.selected {
                    background-color: #ab67ff;
                    border-color: #ab67ff;
                }
            }
        }
    }
    .login-btn, .register-btn {
        width: 100%;
        height: 45px;
        background: linear-gradient(90deg, #d3aaff, #ab67ff);
        color: white;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        margin-bottom: 20px;
        transition: all 0.3s ease;

        &:disabled {
            background: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
        }
    }
    .bottom-links {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        color: #999;
        padding: 0 20px;
        .register-link {
            color: #ab67ff;
        }
    }
}
.customer-service {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: 12px;
    padding: 15px 0;
    background-color: #fff;
    position: relative;
    z-index: 2;
    cursor: pointer;
    img {
        margin-right: 5px;
    }
}
</style>
