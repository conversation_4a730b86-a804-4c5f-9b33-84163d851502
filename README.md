# tg_group_pc

> Pc group html transformation

## Build Setup

``` bash
# install dependencies
npm install


# serve with hot reload at localhost:8080
# 这个是运行测试
npm run dev



# build for production with minification
# 这个是打包
npm run build

# 根目录下dist是打包文件

# build for production and view the bundle analyzer report
npm run build --report
```

For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).
