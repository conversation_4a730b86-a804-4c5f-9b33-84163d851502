<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="96" height="96" viewBox="0 0 96 96">
    <defs>
        <linearGradient id="c" x1="94.858%" x2="13.666%" y1="5.143%" y2="96.124%">
            <stop offset="0%" stop-color="#A154FF"/>
            <stop offset="59.746%" stop-color="#28339F"/>
            <stop offset="100%" stop-color="#A62CFF"/>
        </linearGradient>
        <rect id="a" width="96" height="96" rx="21.328"/>
        <filter id="b" width="121.9%" height="121.9%" x="-10.9%" y="-7.8%" filterUnits="objectBoundingBox">
            <feOffset dy="3" in="SourceAlpha" result="shadowOffsetOuter1"/>
            <feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="3"/>
            <feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0.184313725 0 0 0 0 0.219607843 0 0 0 0 0.494117647 0 0 0 0.297394012 0"/>
        </filter>
        <linearGradient id="e" x1="50%" x2="50%" y1="0%" y2="100%">
            <stop offset="0%" stop-color="#FFF"/>
            <stop offset="100%" stop-color="#FFF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="f" x1="50%" x2="50%" y1="0%" y2="100%">
            <stop offset="0%" stop-color="#FFF"/>
            <stop offset="100%" stop-color="#FFF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="g" x1="75.199%" x2="50%" y1="41.486%" y2="74.432%">
            <stop offset="0%" stop-color="#FFF"/>
            <stop offset="49.229%" stop-color="#C3C3C3"/>
            <stop offset="100%" stop-color="#FFF"/>
        </linearGradient>
    </defs>
    <g fill="none" fill-rule="evenodd">
        <mask id="d" fill="#fff">
            <use xlink:href="#a"/>
        </mask>
        <use fill="#000" filter="url(#b)" xlink:href="#a"/>
        <use fill="url(#c)" xlink:href="#a"/>
        <g mask="url(#d)">
            <path fill="url(#e)" d="M25.792.09H75.42l25.78 36.962H.012z" opacity=".2" transform="translate(-.4 -.09)"/>
            <path fill="url(#f)" d="M36.916.09h27.516l19.196 37.568H15.16z" opacity=".1" transform="translate(-.4 -.09)"/>
        </g>
        <path fill="url(#g)" d="M49.582 19.332c2.82 0 12.635 11.231 14.793 20.374 5.163-4.51 10.722-5.874 11.164-5.874.584 0 1.024.467 1.024 1.087 0 .278-.213.68-.566 1.346-1.122 2.115-3.455 6.517-3.455 14.907l.003.123c.01.351.032 1.007-.345 1.396-.188.193-.445.292-.763.292-.429 0-.76-.296-.825-.735a23.431 23.431 0 0 1-.244-3.386c0-3.856.914-7.893 2.645-11.707-4.248 1.682-6.476 3.805-7.826 5.09-.864.822-1.434 1.366-2.038 1.366-.571 0-.973-.372-1.023-.948-.6-6.831-10.318-20.161-12.961-20.161-.069 0-.192.08-.416.226l-.137.088c-4.796 3.075-11.48 13.908-11.922 19.32-.066.796-.863 1.577-1.61 1.577-.464 0-.895-.425-1.608-1.13-1.196-1.18-3.166-3.127-7.138-4.88 1.527 3.597 2.332 7.34 2.332 10.857 0 .657-.03 1.313-.085 1.952 5.681-1.421 11.921-2.14 18.551-2.14 3.584 0 21.22 1.007 21.22 3.002 0 .53-.439 1.12-1.026 1.12-.744 0-2.567-.178-4.875-.402-4.447-.435-10.537-1.03-15.268-1.03-10.45 0-16.893 1.822-19.318 2.507-.682.194-1.058.3-1.25.3-.632 0-1.056-.454-1.056-1.129 0-.376.133-.768.377-1.113-.066-8.73-2.282-12.933-3.35-14.955-.346-.658-.555-1.055-.555-1.354 0-.82.802-1.626 1.618-1.626.045 0 5.558 1.068 10.884 5.739 3.253-11.546 13.515-20.1 15.049-20.1zm8.299 26.846c0 .335-.303.709-.709.709-.02 0-.129-.007-.314-.02-2.958-.22-5.735-.331-8.253-.331-4.932 0-7.83.41-9.563.657-.693.098-1.194.169-1.542.169-.548 0-.826-.211-.826-.628 0-.37.293-.819.769-.9 3.827-.656 7.367-.975 10.823-.975.097 0 9.615.161 9.615 1.32zm-8.494-20.34c.188 0 .363.078.508.225 2.501 1.956 3.946 4.794 3.946 7.767 0 3.146-1.62 5.775-3.945 6.404a.953.953 0 0 1-.452.12c-.042 0-4.186-.561-4.186-6.212 0-3.241 1.517-6.656 3.608-8.119a.922.922 0 0 1 .521-.184zm-.313 3.004c-.857 1.52-1.292 3.085-1.292 4.657 0 2.01.846 3.353 1.86 4.853.786-1.036 1.17-2.19 1.17-3.523 0-1.948-.816-4.02-1.738-5.987zm17.111 2.503c1.439 0 2.3 4.168 2.3 4.21 0 .409-.38.795-.782.795a.599.599 0 0 1-.49-.258c-.866-1.193-1.204-1.906-1.366-2.25l-.048-.1-.078.067c-.41.353-1.175 1.012-1.718 1.012-.276 0-.568-.184-.568-.59 0-.93 1.541-2.886 2.75-2.886zm-32.254-1.502c1.241 0 2.238 2.306 2.238 2.598 0 .47-.405.913-.833.913-.316 0-1.197-.5-1.715-.913-.693 1.555-1.391 2.407-1.977 2.407-.304 0-.524-.245-.524-.583 0-.347 1.197-4.422 2.811-4.422zm-11.53-2.579c1.96 1.645.178 4.792-2.105 4.792-1.208 0-2.138-.962-2.308-2.16-1-1.45 1.975-4.68 4.413-2.632zm56.956 0c1.961 1.645.179 4.792-2.105 4.792-1.208 0-2.138-.962-2.308-2.16-1-1.45 1.975-4.68 4.413-2.632zm-26.94-16.139c2.614 2.193.237 6.389-2.807 6.389-1.611 0-2.851-1.283-3.078-2.88-1.333-1.932 2.633-6.24 5.884-3.509z" mask="url(#d)"/>
        <path fill="#FFF" fill-rule="nonzero" d="M26.662 63.427c2.778 1.21 2.15 5.623.47 8.714-1.814 3.337-4.636 5.958-7.682 4.838-1.837-.672-2.554-2.352-2.554-4.189 0-4.77 4.189-8.915 6.72-9.81 1.3-.449 2.42-.202 3.046.447zm-1.164 8.087c2.038-3.741 1.948-7.101.716-7.55-1.097-.402-3.942 1.12-6.048 4.391-2.15 3.338-2.195 7.123-.672 7.773 1.479.627 4.122-1.165 6.004-4.614zm8.03-6.16c-.896-.045-1.366-.538-1.523-.896-.112-.247-.045-.359.224-.381 1.568-.135 3.584-1.232 5.085-1.322 1.142-.067 2.24.291 2.643 1.815.403 1.5-.023 2.755-.784 3.74-.65.83-1.837 1.95-3.495 2.666.426.202.784.515 1.076.94.47.695.448 2.017-.516 3.07-.985 1.075-2.665 1.859-3.92 2.083-.85.157-1.388.134-1.881-.112-.448-.224-.851-.605-1.143-1.03-.336-.493-.56-.852-.044-2.285.896-2.51 2.598-5.78 4.278-8.288zm-1.03 4.995c4.435-1.03 7.19-4.055 6.63-5.578-.448-1.165-2.666-.134-4.211.359.045.492-.112 1.164-.672 2.195-.448.829-1.098 1.881-1.747 3.024zm1.724 1.12c-.313.067-.627.112-.94.157-.628.067-1.076 0-1.39-.157-.604 1.165-1.164 2.352-1.5 3.472-.246.784 0 1.053.717.963 1.433-.18 3.36-1.098 4.323-2.285.829-1.03.829-2.038-1.21-2.15zm4.984 4.368c.27-.605.83-.941 1.277-.784.448.157.583.739.336 1.344-.246.582-.829.94-1.277.784-.448-.157-.582-.807-.336-1.344zm12.175-3.651c.112-.247.358-.247.47.044.56 1.501-.873 3.181-1.814 3.853-1.59 1.143-3.472 1.12-4.682.47-1.008-.537-1.881-1.926-1.926-3.763-.09-4.748 3.897-8.803 6.406-9.766 1.21-.47 2.307-.56 3.024.067.493.426 1.008 1.075 1.187 1.635.202.65.135 1.277-.201 1.972-.336.694-.65 1.568-.896 2.217-.067.18-.224.314-.493.135-.515-.336-.874-1.03-.538-2.15.359-1.188 1.165-2.465.65-3.002-.627-.672-4.032 1.142-6.048 4.48-1.971 3.27-1.747 6.585-.65 7.481 1.434 1.165 3.808-.336 5.51-3.673zm13.272-8.759c2.777 1.21 2.15 5.623.47 8.714-1.814 3.337-4.637 5.958-7.683 4.838-1.837-.672-2.554-2.352-2.554-4.189 0-4.77 4.19-8.915 6.72-9.81 1.3-.449 2.42-.202 3.047.447zm-1.165 8.087c2.038-3.741 1.949-7.101.717-7.55-1.098-.402-3.943 1.12-6.048 4.391-2.15 3.338-2.195 7.123-.672 7.773 1.478.627 4.121-1.165 6.003-4.614zm9.509-4.458c-1.994 3.002-4.503 7.011-5.824 10.013-.09.224-.224.224-.381.045-.314-.336-.56-1.098-.045-2.442.829-2.128 4.458-8.109 6.877-11.402.202-.29.426-.38.717-.134.873.762 1.12 1.546.537 3.315a145.696 145.696 0 0 0-2.374 7.907c2.442-3.987 5.712-7.324 8.176-11.155.202-.313.38-.336.65-.067.896.918.672 2.15.067 3.27-1.568 2.912-3.808 7.437-4.928 10.663-.09.224-.18.224-.359.067-.358-.291-.627-.963-.246-2.442.336-1.321 1.77-4.457 3.226-7.257-2.554 3.091-5.152 6.518-6.765 9.52-.112.224-.224.291-.403.157-.74-.538-1.076-1.322-.852-2.71.202-1.278.941-4.369 1.927-7.348z" mask="url(#d)"/>
    </g>
</svg>
